import json
from collections import defaultdict

def create_speaker_mapping(raw_file_path, matched_file_path):
    """
    根据两个字幕文件创建speaker映射关系
    
    Args:
        raw_file_path: 原始字幕文件路径 (subtitle_output_raw_011.txt)
        matched_file_path: 匹配后的字幕文件路径 (matched_subtitles.json)
    
    Returns:
        dict: speaker映射关系，key是matched文件中的speaker，value是对应的原始speaker名称集合
    """
    
    # 读取原始字幕文件
    with open(raw_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        # 移除markdown代码块标记
        if content.startswith('```json'):
            content = content.replace('```json', '').replace('```', '')
        raw_data = json.loads(content.strip())
    
    # 读取匹配后的字幕文件
    with open(matched_file_path, 'r', encoding='utf-8') as f:
        matched_data = json.load(f)
    
    # 创建映射关系
    speaker_mapping = defaultdict(set)
    
    # 确保两个文件的长度一致
    if len(raw_data) != len(matched_data):
        print(f"警告: 文件长度不一致。原始文件: {len(raw_data)}, 匹配文件: {len(matched_data)}")
        min_length = min(len(raw_data), len(matched_data))
    else:
        min_length = len(raw_data)
    
    # 根据索引位置建立映射关系
    for i in range(min_length):
        raw_speaker = raw_data[i]['speaker']
        matched_speaker = matched_data[i]['speaker']
        
        # 将原始speaker名称添加到对应的matched speaker的集合中
        speaker_mapping[matched_speaker].add(raw_speaker)
    
    # 将set转换为list以便JSON序列化
    result = {}
    for matched_speaker, raw_speakers in speaker_mapping.items():
        result[matched_speaker] = list(raw_speakers)
    
    return result

def save_mapping_to_file(mapping, output_path):
    """
    将映射关系保存到文件
    
    Args:
        mapping: speaker映射关系
        output_path: 输出文件路径
    """
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(mapping, f, ensure_ascii=False, indent=2)

def print_mapping_summary(mapping):
    """
    打印映射关系摘要
    
    Args:
        mapping: speaker映射关系
    """
    print("Speaker映射关系:")
    print("=" * 50)
    
    for matched_speaker, raw_speakers in mapping.items():
        print(f"{matched_speaker}: {raw_speakers}")
    
    print("\n映射统计:")
    print(f"总共有 {len(mapping)} 个matched speaker")
    
    all_raw_speakers = set()
    for raw_speakers in mapping.values():
        all_raw_speakers.update(raw_speakers)
    print(f"对应 {len(all_raw_speakers)} 个不同的原始speaker: {list(all_raw_speakers)}")

def main():
    # 文件路径
    raw_file = "subtitle_output_raw_011.txt"
    matched_file = "归档/chapter_011/matched_subtitles.json"
    output_file = "speaker_mapping_011.json"
    
    try:
        # 创建映射关系
        mapping = create_speaker_mapping(raw_file, matched_file)
        
        # 打印映射关系
        print_mapping_summary(mapping)
        
        # 保存到文件
        save_mapping_to_file(mapping, output_file)
        print(f"\n映射关系已保存到: {output_file}")
        
        return mapping
        
    except FileNotFoundError as e:
        print(f"错误: 找不到文件 {e}")
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 {e}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    mapping = main()
