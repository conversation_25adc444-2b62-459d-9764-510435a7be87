# 字幕与说话人匹配工具使用说明

## 功能简介

这个Python工具可以自动将`speakers.json`文件中的说话人信息与`ori.srt`字幕文件进行匹配，生成带有说话人标识的字幕文件。

## 文件结构要求

```
归档/
├── chapter_002/
│   ├── ori.srt          # SRT字幕文件
│   ├── speakers.json    # 说话人映射文件
│   └── ...
├── chapter_003/
│   ├── ori.srt
│   ├── speakers.json
│   └── ...
└── ...
```

## 输入文件格式

### speakers.json 格式
```json
{
    "0": "speaker_1",
    "1": "speaker_1", 
    "2": "speaker_2",
    "3": "speaker_1"
}
```
- 键：字幕序号（从0开始）
- 值：说话人标识

### ori.srt 格式
标准SRT字幕文件格式：
```
0
00:00:02,460 --> 00:00:08,740
各位，这是我婆婆邱小花，是咱们花绣街的唯一传人。

1
00:00:10,180 --> 00:00:15,380
说来惭愧，为了给婆婆治病，我们是房子也卖了。
```

## 使用方法

### 方法1：直接运行（处理所有章节）
```bash
python subtitle_speaker_matcher.py
```

### 方法2：在代码中使用
```python
from subtitle_speaker_matcher import SubtitleSpeakerMatcher

# 创建匹配器实例
matcher = SubtitleSpeakerMatcher()

# 处理单个章节
success = matcher.process_single_chapter("归档/chapter_002", ['srt', 'txt'])

# 处理所有章节
results = matcher.process_all_chapters("归档", ['srt', 'txt', 'json'])
```

## 输出文件

工具会在每个章节文件夹中生成以下文件：

### 1. matched_subtitles.srt
带说话人信息的SRT字幕文件：
```
0
00:00:02,460 --> 00:00:08,740
[speaker_1] 各位，这是我婆婆邱小花，是咱们花绣街的唯一传人。

1
00:00:10,180 --> 00:00:15,380
[speaker_1] 说来惭愧，为了给婆婆治病，我们是房子也卖了。
```

### 2. matched_subtitles.txt
简化的文本格式：
```
00:00:02,460 --> 00:00:08,740 | speaker_1 | 各位，这是我婆婆邱小花，是咱们花绣街的唯一传人。
00:00:10,180 --> 00:00:15,380 | speaker_1 | 说来惭愧，为了给婆婆治病，我们是房子也卖了。
```

### 3. matched_subtitles.json
JSON格式数据：
```json
[
  {
    "index": 0,
    "start_time": "00:00:02,460",
    "end_time": "00:00:08,740", 
    "text": "各位，这是我婆婆邱小花，是咱们花绣街的唯一传人。",
    "speaker": "speaker_1"
  }
]
```

## 主要功能特性

- **自动批量处理**：一次性处理所有chapter文件夹
- **多种输出格式**：支持SRT、TXT、JSON三种格式
- **错误处理**：自动跳过无效文件，显示处理状态
- **编码兼容**：支持UTF-8和GBK编码
- **灵活配置**：可以选择性生成不同格式的输出文件

## 错误处理

工具会自动处理以下情况：
- 文件不存在
- 编码问题（自动尝试UTF-8和GBK）
- JSON格式错误
- SRT格式不规范
- 说话人映射缺失（显示为"未知说话人"）

## 运行结果示例

```
字幕与说话人匹配工具
========================================
找到 10 个章节文件夹

处理章节: chapter_002
  SRT文件: 归档\chapter_002\ori.srt
  说话人文件: 归档\chapter_002\speakers.json
  成功匹配 16 条字幕
  生成输出文件: matched_subtitles.srt
  生成输出文件: matched_subtitles.txt
  生成输出文件: matched_subtitles.json

=== 处理完成 ===
成功处理: 10/10 个章节
```

## 注意事项

1. 确保Python环境已安装（推荐Python 3.7+）
2. 字幕序号必须从0开始连续编号
3. speakers.json中的键必须是字符串格式的数字
4. 如果说话人映射不完整，缺失的部分会显示为"未知说话人"
5. 输出文件会覆盖已存在的同名文件

## 自定义配置

如需修改输出格式或添加新功能，可以编辑`subtitle_speaker_matcher.py`文件中的相关方法：
- `_generate_srt_output()`: 修改SRT输出格式
- `_generate_txt_output()`: 修改TXT输出格式  
- `_generate_json_output()`: 修改JSON输出格式
