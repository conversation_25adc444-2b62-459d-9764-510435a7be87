=== Gemini Audio SRT API 错误信息 ===
状态码: 500
==================================================
错误详情:
{"detail":"处理过程中出现错误: 400 INVALID_ARGUMENT. {'error': {'code': 400, 'message': 'Cannot fetch content from the provided URL. Please ensure the URL is valid and accessible by Vertex AI. Vertex AI respects robots.txt rules, so confirm the URL is allowed to be crawled. Status: URL_REJECTED-REJECTED_FC_GLOBAL_BACKOFF', 'status': 'INVALID_ARGUMENT'}}"}
