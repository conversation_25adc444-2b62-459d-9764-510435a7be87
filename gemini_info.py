import os
import json
from google import genai
from google.genai.types import (
    FunctionDeclaration,
    GenerateContentConfig,
    GoogleSearch,
    HarmBlockThreshold,
    HarmCategory,
    MediaResolution,
    Part,
    Retrieval,
    SafetySetting,
    Tool,
    ToolCodeExecution,
    VertexAISearch,
)

PROJECT_ID = "gen-lang-client-0255558294"

LOCATION = os.environ.get("GOOGLE_CLOUD_REGION", "global")

client = genai.Client(vertexai=True, project=PROJECT_ID, location=LOCATION)

MODEL_ID = "gemini-2.5-pro"

# 读取SRT文件内容
def read_srt_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print(f"文件 {file_path} 未找到")
        return ""
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return ""

# 读取SRT文件
srt_content = read_srt_file("subtitle_output_raw_0902.txt")

# 构建包含SRT内容的prompt
prompt = f"""根据这段音频，参考提供的srt字幕，生成角色表。输出结果为json格式。

****要求***
1.输出内容包含人物名称，人物别名列表，以及证据
2.每个人物别名必须对应证据，证据需要从字幕里获取。人物别名不要太抽象，需要合情合理，比如小明、指代等等，不要用男的、女的，我，你这些太抽象的词。
3.证据需要确凿，不要胡编乱造。
4.出场的小人物不要在角色表中体现。比如路人甲、路人乙、路人丙、群众等等。

*参考的SRT文件内容：
{srt_content}
"""


response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(
            file_uri="https://gidfyoqecthbormpbirt.supabase.co/storage/v1/object/public/audios/merged_0902.mp3",
            mime_type="audio/mpeg",
        ),
        prompt,
    ],
    config=GenerateContentConfig(audio_timestamp=True, temperature=1.0),
)

# 保存JSON输出到文件
try:
    # 解析JSON响应
    json_data = json.loads(response.text)
    
    # 保存到JSON文件
    output_file = "role_output_0902.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    
    print(f"JSON输出已保存到 {output_file}")
    print("输出内容:")
    print(json.dumps(json_data, ensure_ascii=False, indent=2))
    
except json.JSONDecodeError as e:
    print(f"JSON解析错误: {e}")
    print("原始响应:")
    print(response.text)
    
    # 如果JSON解析失败，保存原始文本
    with open("role_output_raw_0902.txt", 'w', encoding='utf-8') as f:
        f.write(response.text)
    print("原始响应已保存到 role_output_raw_0902.txt")
    
except Exception as e:
    print(f"保存文件时出错: {e}")
    print("原始响应:")
    print(response.text)