#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频字幕处理器 - 简化版
合并音频文件和字幕，使用Gemini进行角色识别
"""

import os
import re
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Tuple
from google import genai
from google.genai.types import GenerateContentConfig, Part

# Gemini配置
PROJECT_ID = "gen-lang-client-0255558294"
LOCATION = os.environ.get("GOOGLE_CLOUD_REGION", "global")
client = genai.Client(vertexai=True, project=PROJECT_ID, location=LOCATION)
MODEL_ID = "gemini-2.5-pro"


class AudioSubtitleProcessor:
    """音频字幕处理器"""
    
    def __init__(self, archive_dir: str = "归档"):
        self.archive_dir = archive_dir
        
    def get_audio_duration(self, audio_file: str) -> float:
        """获取音频文件时长（秒）"""
        try:
            cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', str(audio_file)]
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            if result.returncode == 0:
                return float(json.loads(result.stdout)['format']['duration'])
            return 0.0
        except Exception:
            return 0.0

    def parse_srt_time(self, time_str: str) -> float:
        """SRT时间格式转秒数"""
        time_str = time_str.replace(',', '.')
        parts = time_str.split(':')
        return int(parts[0]) * 3600 + int(parts[1]) * 60 + float(parts[2])

    def format_srt_time(self, seconds: float) -> str:
        """秒数转SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')

    def parse_srt_file(self, file_path: str) -> List[Dict]:
        """解析SRT文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read().strip()
        
        subtitles = []
        for block in content.split('\n\n'):
            if not block.strip():
                continue
            lines = block.strip().split('\n')
            if len(lines) < 3:
                continue
            
            time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', lines[1])
            if time_match:
                subtitles.append({
                    'index': int(lines[0]),
                    'start_time': time_match.group(1),
                    'end_time': time_match.group(2),
                    'start_seconds': self.parse_srt_time(time_match.group(1)),
                    'end_seconds': self.parse_srt_time(time_match.group(2)),
                    'text': '\n'.join(lines[2:])
                })
        return subtitles

    def get_chapter_folders(self) -> List[str]:
        """获取章节文件夹列表"""
        archive_path = Path(self.archive_dir)
        if not archive_path.exists():
            return []
        
        chapter_folders = [item.name for item in archive_path.iterdir() 
                          if item.is_dir() and item.name.startswith('chapter_')]
        
        def extract_chapter_number(folder_name):
            match = re.search(r'chapter_(\d+)', folder_name)
            return int(match.group(1)) if match else 0
        
        return sorted(chapter_folders, key=extract_chapter_number)

    def concatenate_audio_files(self, audio_files: List[Path], output_file: str) -> bool:
        """拼接音频文件"""
        try:
            filelist_path = "temp_audio_filelist.txt"
            with open(filelist_path, 'w', encoding='utf-8') as f:
                for audio_file in audio_files:
                    escaped_path = str(audio_file).replace("'", "'\"'\"'")
                    f.write(f"file '{escaped_path}'\n")
            
            cmd = ['ffmpeg', '-f', 'concat', '-safe', '0', '-i', filelist_path, '-c', 'copy', '-y', str(output_file)]
            result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
            
            if os.path.exists(filelist_path):
                os.remove(filelist_path)
            
            return result.returncode == 0
        except Exception:
            return False

    def merge_subtitles(self, subtitle_files: List[Tuple[str, float]], output_file: str) -> bool:
        """合并字幕文件"""
        try:
            all_subtitles = []
            current_offset = 0.0
            subtitle_index = 1
            
            for srt_file, duration in subtitle_files:
                subtitles = self.parse_srt_file(srt_file)
                for sub in subtitles:
                    all_subtitles.append({
                        'index': subtitle_index,
                        'start_time': self.format_srt_time(sub['start_seconds'] + current_offset),
                        'end_time': self.format_srt_time(sub['end_seconds'] + current_offset),
                        'text': sub['text']
                    })
                    subtitle_index += 1
                current_offset += duration
            
            with open(output_file, 'w', encoding='utf-8') as f:
                for sub in all_subtitles:
                    f.write(f"{sub['index']}\n{sub['start_time']} --> {sub['end_time']}\n{sub['text']}\n\n")
            
            return True
        except Exception:
            return False

    def gemini_role_analysis(self, audio_url: str, srt_content: str, output_file: str) -> bool:
        """使用Gemini分析角色"""
        try:
            prompt = f"""根据这段音频，参考提供的srt字幕，识别音频中的说话人，台词。返回字段包含speaker, start_time, end_time, gender, text。返回json格式。

**要求**：
1. 不要修改参考SRT文件内容的时间轴。时间轴完全和原来的字幕保持完全一致。
2. 根据上下文识别speaker，要求speaker能做到上下文合理。speaker需要为中文。
3. start_time, end_time格式为00:14:03,557，注意逗号是英文逗号。
4. gender为男或女。根据音频识别说话人的性别。

*参考的SRT文件内容：
{srt_content}

请根据音频内容生成准确的字幕信息。"""

            response = client.models.generate_content(
                model=MODEL_ID,
                contents=[
                    Part.from_uri(file_uri=audio_url, mime_type="audio/mpeg"),
                    prompt,
                ],
                config=GenerateContentConfig(audio_timestamp=True, temperature=1.0),
            )

            # 解析JSON响应
            try:
                json_data = json.loads(response.text)
            except json.JSONDecodeError:
                content = response.text.strip()
                if content.startswith('```json'):
                    content = content[7:]
                elif content.startswith('```'):
                    content = content[3:]
                if content.endswith('```'):
                    content = content[:-3]
                content = content.strip()
                
                try:
                    json_data = json.loads(content)
                except json.JSONDecodeError:
                    with open(f"{output_file}_raw.txt", 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    return False

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception:
            return False

    def generate_speaker_srt(self, gemini_json: str, output_file: str) -> bool:
        """根据Gemini JSON生成带说话人的SRT文件"""
        try:
            with open(gemini_json, 'r', encoding='utf-8') as f:
                gemini_data = json.load(f)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                for i, item in enumerate(gemini_data, 1):
                    f.write(f"{i}\n")
                    f.write(f"{item['start_time']} --> {item['end_time']}\n")
                    speaker = item.get('speaker', '未知')
                    gender = item.get('gender', '')
                    gender_mark = f"({gender})" if gender else ""
                    f.write(f"[{speaker}{gender_mark}] {item['text']}\n\n")
            
            return True
        except Exception:
            return False

    def process_all(self, audio_url: str) -> Dict[str, str]:
        """处理所有步骤"""
        results = {}
        
        # 检查FFmpeg
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        except Exception:
            results['error'] = "FFmpeg未安装或不可用"
            return results
        
        # 获取章节文件夹
        chapter_folders = self.get_chapter_folders()
        if not chapter_folders:
            results['error'] = f"在 {self.archive_dir} 目录下未找到章节文件夹"
            return results
        
        # 收集音频和字幕文件
        audio_files = []
        subtitle_files = []
        
        for folder in chapter_folders:
            chapter_path = Path(self.archive_dir) / folder
            audio_file = chapter_path / "ori_audio.wav"
            srt_file = chapter_path / "ori.srt"
            
            if audio_file.exists() and srt_file.exists():
                duration = self.get_audio_duration(str(audio_file))
                if duration > 0:
                    audio_files.append(audio_file)
                    subtitle_files.append((str(srt_file), duration))
        
        if not audio_files:
            results['error'] = "未找到有效的音频文件"
            return results
        
        # 合并音频和字幕
        output_audio = "merged_all_chapters.wav"
        output_subtitle = "merged_all_chapters.srt"
        
        if not self.concatenate_audio_files(audio_files, output_audio):
            results['error'] = "音频合并失败"
            return results
        results['audio'] = output_audio
        
        if not self.merge_subtitles(subtitle_files, output_subtitle):
            results['error'] = "字幕合并失败"
            return results
        results['subtitle'] = output_subtitle
        
        # Gemini角色分析
        with open(output_subtitle, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        gemini_output = "gemini_role_analysis.json"
        if self.gemini_role_analysis(audio_url, srt_content, gemini_output):
            results['gemini_analysis'] = gemini_output
            
            # 生成带说话人的SRT
            speaker_srt = "merged_all_chapters_with_speaker.srt"
            if self.generate_speaker_srt(gemini_output, speaker_srt):
                results['speaker_srt'] = speaker_srt
        
        return results


def main():
    """主函数"""
    processor = AudioSubtitleProcessor()
    audio_url = "https://gidfyoqecthbormpbirt.supabase.co/storage/v1/object/public/audios/merged_0902.mp3"
    
    results = processor.process_all(audio_url)
    
    if 'error' in results:
        print(f"错误: {results['error']}")
        return 1
    
    print("处理完成!")
    for key, value in results.items():
        print(f"{key}: {value}")
    
    return 0


if __name__ == "__main__":
    main()
