#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
角色语音提取工具使用示例
"""

from character_voice_extractor import CharacterVoiceExtractor
import os

def example_usage():
    """使用示例"""
    
    # 示例文件路径（请根据实际情况修改）
    audio_file = "combined_audio.wav"  # 原始音频文件
    subtitle_file = "subtitle_output_raw_003.txt"  # 字幕文件
    output_dir = "extracted_voices"  # 输出目录
    
    print("角色语音提取工具使用示例")
    print("=" * 50)
    
    try:
        # 1. 创建提取器
        print("1. 创建语音提取器...")
        extractor = CharacterVoiceExtractor(output_format='wav')
        
        # 2. 加载字幕数据
        print("2. 加载字幕数据...")
        subtitle_data = extractor.load_subtitle_data(subtitle_file)
        
        # 3. 获取所有角色
        print("3. 分析角色信息...")
        characters = extractor.get_characters(subtitle_data)
        print(f"发现角色: {', '.join(characters)}")
        
        # 4. 提取所有角色的语音（严格按字典条目）
        print("4. 开始提取角色语音...")
        results = extractor.extract_character_segments(
            audio_file=audio_file,
            subtitle_data=subtitle_data,
            output_dir=output_dir,
            target_character=None,  # 提取所有角色
            merge_close_segments=False,  # 严格按字典条目提取，不合并
            min_gap_seconds=0.5  # 如果启用合并，0.5秒内的片段会被合并
        )
        
        # 5. 创建摘要报告
        print("5. 生成摘要报告...")
        extractor.create_character_summary(results, output_dir)
        
        print("\n提取完成！")
        print(f"输出目录: {output_dir}")
        
        return results
        
    except Exception as e:
        print(f"错误: {e}")
        return None

def extract_single_character_example():
    """提取单个角色的示例"""
    
    audio_file = "combined_audio.wav"
    subtitle_file = "subtitle_output_raw_003.txt"
    output_dir = "single_character_voice"
    target_character = "唐婉婉"  # 只提取这个角色
    
    print(f"\n提取单个角色示例: {target_character}")
    print("=" * 50)
    
    try:
        extractor = CharacterVoiceExtractor(output_format='mp3')  # 使用mp3格式
        
        subtitle_data = extractor.load_subtitle_data(subtitle_file)
        
        results = extractor.extract_character_segments(
            audio_file=audio_file,
            subtitle_data=subtitle_data,
            output_dir=output_dir,
            target_character=target_character,
            merge_close_segments=False,  # 严格按字典条目提取
            min_gap_seconds=1.0  # 如果启用合并，1秒内的片段会被合并
        )
        
        extractor.create_character_summary(results, output_dir)
        
        print(f"\n{target_character} 的语音提取完成！")
        print(f"输出目录: {output_dir}")
        
        return results
        
    except Exception as e:
        print(f"错误: {e}")
        return None

def analyze_subtitle_file():
    """分析字幕文件内容"""
    
    subtitle_file = "subtitle_output_raw_003.txt"
    
    print("\n字幕文件分析")
    print("=" * 50)
    
    try:
        extractor = CharacterVoiceExtractor()
        subtitle_data = extractor.load_subtitle_data(subtitle_file)
        
        print(f"总字幕条数: {len(subtitle_data)}")
        
        # 统计角色信息
        character_stats = {}
        total_duration = 0
        
        for item in subtitle_data:
            speaker = item.get('speaker', '未知')
            
            if speaker not in character_stats:
                character_stats[speaker] = {
                    'count': 0,
                    'total_duration': 0,
                    'texts': []
                }
            
            character_stats[speaker]['count'] += 1
            character_stats[speaker]['texts'].append(item['text'])
            
            # 计算时长
            try:
                start = extractor._parse_time_to_seconds(item['start_time'])
                end = extractor._parse_time_to_seconds(item['end_time'])
                duration = end - start
                character_stats[speaker]['total_duration'] += duration
                total_duration += duration
            except:
                pass
        
        print(f"总时长: {total_duration:.1f} 秒")
        print("\n角色统计:")
        
        for character, stats in sorted(character_stats.items()):
            print(f"\n{character}:")
            print(f"  对话数量: {stats['count']}")
            print(f"  总时长: {stats['total_duration']:.1f} 秒")
            print(f"  平均时长: {stats['total_duration']/stats['count']:.1f} 秒/条")
            
            # 显示前3条对话作为示例
            print("  示例对话:")
            for i, text in enumerate(stats['texts'][:3]):
                print(f"    {i+1}. {text}")
            if len(stats['texts']) > 3:
                print(f"    ... 还有 {len(stats['texts'])-3} 条")
        
    except Exception as e:
        print(f"分析失败: {e}")

if __name__ == '__main__':
    # 首先分析字幕文件
    analyze_subtitle_file()
    
    # 检查音频文件是否存在
    if os.path.exists("combined_audio.wav"):
        print("\n" + "="*60)
        # 提取所有角色
        results = example_usage()
        
        if results:
            print("\n" + "="*60)
            # 提取单个角色示例
            extract_single_character_example()
    else:
        print("\n注意: 未找到音频文件 'combined_audio.wav'")
        print("请确保音频文件存在，或修改脚本中的文件路径")
