#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕与说话人匹配工具
功能：将speakers.json中的说话人信息与ori.srt字幕文件进行匹配
"""

import json
import os
import re
from typing import Dict, List, Tuple, Optional
from pathlib import Path


class SubtitleSpeakerMatcher:
    """字幕与说话人匹配器"""
    
    def __init__(self):
        pass
    
    def parse_srt_file(self, srt_path: str) -> List[Dict]:
        """
        解析SRT字幕文件
        
        Args:
            srt_path: SRT文件路径
            
        Returns:
            包含字幕信息的列表，每个元素包含：
            - index: 字幕序号
            - start_time: 开始时间
            - end_time: 结束时间
            - text: 字幕内容
        """
        subtitles = []
        
        if not os.path.exists(srt_path):
            print(f"警告: SRT文件不存在: {srt_path}")
            return subtitles
            
        try:
            with open(srt_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(srt_path, 'r', encoding='gbk') as f:
                    content = f.read().strip()
            except UnicodeDecodeError:
                print(f"错误: 无法读取文件 {srt_path}，编码问题")
                return subtitles
        
        # 按空行分割字幕块
        subtitle_blocks = re.split(r'\n\s*\n', content)
        
        for block in subtitle_blocks:
            if not block.strip():
                continue
                
            lines = block.strip().split('\n')
            if len(lines) < 3:
                continue
                
            try:
                # 解析序号
                index = int(lines[0].strip())
                
                # 解析时间轴
                time_line = lines[1].strip()
                time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
                if not time_match:
                    continue
                    
                start_time = time_match.group(1)
                end_time = time_match.group(2)
                
                # 解析字幕内容（可能有多行）
                text_lines = lines[2:]
                text = '\n'.join(text_lines)
                
                subtitles.append({
                    'index': index,
                    'start_time': start_time,
                    'end_time': end_time,
                    'text': text
                })
                
            except (ValueError, IndexError) as e:
                print(f"警告: 解析字幕块时出错: {block[:50]}...")
                continue
        
        return subtitles
    
    def load_speakers_mapping(self, speakers_path: str) -> Dict[int, str]:
        """
        加载说话人映射文件
        
        Args:
            speakers_path: speakers.json文件路径
            
        Returns:
            字幕序号到说话人的映射字典
        """
        if not os.path.exists(speakers_path):
            print(f"警告: speakers.json文件不存在: {speakers_path}")
            return {}
            
        try:
            with open(speakers_path, 'r', encoding='utf-8') as f:
                speakers_data = json.load(f)
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            print(f"错误: 无法解析 {speakers_path}: {e}")
            return {}
        
        # 转换键为整数
        mapping = {}
        for key, value in speakers_data.items():
            try:
                mapping[int(key)] = value
            except ValueError:
                print(f"警告: 无效的键值: {key}")
                continue
                
        return mapping
    
    def match_subtitles_with_speakers(self, subtitles: List[Dict], speakers_mapping: Dict[int, str]) -> List[Dict]:
        """
        将字幕与说话人进行匹配
        
        Args:
            subtitles: 字幕列表
            speakers_mapping: 说话人映射
            
        Returns:
            包含说话人信息的字幕列表
        """
        matched_subtitles = []
        
        for subtitle in subtitles:
            # SRT文件中的序号和speakers.json中的键都是从0开始
            speaker_key = subtitle['index']
            speaker = speakers_mapping.get(speaker_key, "未知说话人")
            
            matched_subtitle = subtitle.copy()
            matched_subtitle['speaker'] = speaker
            matched_subtitles.append(matched_subtitle)
        
        return matched_subtitles
    
    def generate_output(self, matched_subtitles: List[Dict], output_format: str = 'srt') -> str:
        """
        生成输出内容
        
        Args:
            matched_subtitles: 匹配后的字幕列表
            output_format: 输出格式 ('srt', 'txt', 'json')
            
        Returns:
            格式化的输出内容
        """
        if output_format == 'srt':
            return self._generate_srt_output(matched_subtitles)
        elif output_format == 'txt':
            return self._generate_txt_output(matched_subtitles)
        elif output_format == 'json':
            return self._generate_json_output(matched_subtitles)
        else:
            raise ValueError(f"不支持的输出格式: {output_format}")
    
    def _generate_srt_output(self, matched_subtitles: List[Dict]) -> str:
        """生成SRT格式输出"""
        output_lines = []
        
        for subtitle in matched_subtitles:
            output_lines.append(str(subtitle['index']))
            output_lines.append(f"{subtitle['start_time']} --> {subtitle['end_time']}")
            output_lines.append(f"[{subtitle['speaker']}] {subtitle['text']}")
            output_lines.append("")  # 空行分隔
            
        return '\n'.join(output_lines)
    
    def _generate_txt_output(self, matched_subtitles: List[Dict]) -> str:
        """生成TXT格式输出"""
        output_lines = []
        
        for subtitle in matched_subtitles:
            time_range = f"{subtitle['start_time']} --> {subtitle['end_time']}"
            speaker = subtitle['speaker']
            text = subtitle['text']
            
            output_lines.append(f"{time_range} | {speaker} | {text}")
            
        return '\n'.join(output_lines)
    
    def _generate_json_output(self, matched_subtitles: List[Dict]) -> str:
        """生成JSON格式输出"""
        return json.dumps(matched_subtitles, ensure_ascii=False, indent=2)
    
    def process_single_chapter(self, chapter_path: str, output_formats: List[str] = ['srt', 'txt']) -> bool:
        """
        处理单个章节
        
        Args:
            chapter_path: 章节文件夹路径
            output_formats: 输出格式列表
            
        Returns:
            处理是否成功
        """
        chapter_path = Path(chapter_path)
        srt_path = chapter_path / "ori.srt"
        speakers_path = chapter_path / "speakers.json"
        
        print(f"\n处理章节: {chapter_path.name}")
        print(f"  SRT文件: {srt_path}")
        print(f"  说话人文件: {speakers_path}")
        
        # 检查文件是否存在
        if not srt_path.exists():
            print(f"  错误: SRT文件不存在")
            return False
            
        if not speakers_path.exists():
            print(f"  错误: speakers.json文件不存在")
            return False
        
        # 解析文件
        subtitles = self.parse_srt_file(str(srt_path))
        speakers_mapping = self.load_speakers_mapping(str(speakers_path))
        
        if not subtitles:
            print(f"  错误: 无法解析字幕文件")
            return False
            
        if not speakers_mapping:
            print(f"  错误: 无法解析说话人文件")
            return False
        
        # 匹配字幕和说话人
        matched_subtitles = self.match_subtitles_with_speakers(subtitles, speakers_mapping)
        
        print(f"  成功匹配 {len(matched_subtitles)} 条字幕")
        
        # 生成输出文件
        success = True
        for fmt in output_formats:
            try:
                output_content = self.generate_output(matched_subtitles, fmt)
                output_filename = f"matched_subtitles.{fmt}"
                output_path = chapter_path / output_filename
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(output_content)
                    
                print(f"  生成输出文件: {output_filename}")
                
            except Exception as e:
                print(f"  错误: 生成{fmt}格式输出时失败: {e}")
                success = False
        
        return success
    
    def process_all_chapters(self, archive_path: str, output_formats: List[str] = ['srt', 'txt']) -> Dict[str, bool]:
        """
        处理所有章节
        
        Args:
            archive_path: 归档文件夹路径
            output_formats: 输出格式列表
            
        Returns:
            处理结果字典，键为章节名，值为处理是否成功
        """
        archive_path = Path(archive_path)
        results = {}
        
        if not archive_path.exists():
            print(f"错误: 归档路径不存在: {archive_path}")
            return results
        
        # 查找所有chapter文件夹
        chapter_dirs = [d for d in archive_path.iterdir() 
                       if d.is_dir() and d.name.startswith('chapter_')]
        
        if not chapter_dirs:
            print(f"警告: 在 {archive_path} 中未找到chapter文件夹")
            return results
        
        # 排序章节文件夹
        chapter_dirs.sort(key=lambda x: x.name)
        
        print(f"找到 {len(chapter_dirs)} 个章节文件夹")
        
        # 处理每个章节
        for chapter_dir in chapter_dirs:
            success = self.process_single_chapter(str(chapter_dir), output_formats)
            results[chapter_dir.name] = success
        
        # 输出总结
        print(f"\n=== 处理完成 ===")
        successful = sum(1 for success in results.values() if success)
        total = len(results)
        print(f"成功处理: {successful}/{total} 个章节")
        
        if successful < total:
            print("失败的章节:")
            for chapter, success in results.items():
                if not success:
                    print(f"  - {chapter}")
        
        return results


def main():
    """主函数"""
    print("字幕与说话人匹配工具")
    print("=" * 40)
    
    # 创建匹配器实例
    matcher = SubtitleSpeakerMatcher()
    
    # 设置归档路径
    archive_path = "归档"
    
    # 设置输出格式
    output_formats = ['srt', 'txt', 'json']
    
    # 处理所有章节
    results = matcher.process_all_chapters(archive_path, output_formats)
    
    if results:
        print("\n处理完成！")
        print("生成的文件包含以下格式:")
        print("- matched_subtitles.srt: 带说话人信息的SRT字幕文件")
        print("- matched_subtitles.txt: 简化的文本格式")
        print("- matched_subtitles.json: JSON格式数据")
    else:
        print("\n未找到可处理的章节文件夹")


if __name__ == "__main__":
    main()
