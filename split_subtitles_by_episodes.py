#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按分集分割字幕工具
根据合并音频的时间轴信息，将整合字幕按照分集分割并保存到文件夹中
"""

import os
import json
import re
from pathlib import Path
from typing import List, Dict, Any

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数 (HH:MM:SS,mmm)"""
    time_str = time_str.replace(',', '.')
    parts = time_str.split(':')
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = float(parts[2])
    return hours * 3600 + minutes * 60 + seconds

def format_srt_time(seconds: float) -> str:
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')

def load_timeline_info(timeline_file: str) -> List[Dict]:
    """加载时间轴信息"""
    try:
        with open(timeline_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get('timeline', [])
    except Exception as e:
        print(f"❌ 无法加载时间轴文件 {timeline_file}: {e}")
        return []

def load_subtitle_data(subtitle_file: str) -> List[Dict]:
    """加载字幕数据"""
    try:
        with open(subtitle_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            
        # 移除开头和结尾的```json```标记
        if content.startswith('```json'):
            content = content[7:]  # 移除```json
        if content.endswith('```'):
            content = content[:-3]  # 移除```
            
        # 解析JSON
        subtitle_data = json.loads(content)
        return subtitle_data
        
    except Exception as e:
        print(f"❌ 无法加载字幕文件 {subtitle_file}: {e}")
        return []

def split_subtitles_by_episodes(subtitle_data: List[Dict], timeline_info: List[Dict], output_dir: str):
    """按分集分割字幕"""
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    print(f"📁 输出目录: {output_dir}")
    print(f"📊 分集时间分布:")
    
    # 显示分集信息
    for episode in timeline_info:
        chapter_name = episode['chapter']
        start_time = episode['start_time']
        end_time = episode['end_time']
        duration = episode['duration']
        print(f"   • {chapter_name}: {start_time:.2f}s - {end_time:.2f}s (时长: {duration:.2f}s)")
    
    print()
    
    # 为每个分集创建字幕列表
    episodes_subtitles = {}
    
    for episode in timeline_info:
        chapter_name = episode['chapter']
        episodes_subtitles[chapter_name] = []
    
    # 分配字幕到对应的分集
    for subtitle in subtitle_data:
        try:
            start_seconds = parse_srt_time(subtitle['start_time'])
            
            # 找到字幕属于哪个分集
            found_episode = None
            for episode in timeline_info:
                chapter_name = episode['chapter']
                episode_start = episode['start_time']
                episode_end = episode['end_time']
                
                if episode_start <= start_seconds < episode_end:
                    found_episode = chapter_name
                    break
            
            # 如果没找到，可能属于最后一个分集（允许稍微超出）
            if not found_episode:
                last_episode = timeline_info[-1]
                if start_seconds >= last_episode['start_time']:
                    found_episode = last_episode['chapter']
            
            if found_episode:
                # 调整时间轴到分集内的相对时间
                episode_info = next(e for e in timeline_info if e['chapter'] == found_episode)
                episode_start = episode_info['start_time']
                
                # 计算相对时间
                relative_start = parse_srt_time(subtitle['start_time']) - episode_start
                relative_end = parse_srt_time(subtitle['end_time']) - episode_start
                
                # 确保时间不为负数
                relative_start = max(0, relative_start)
                relative_end = max(0, relative_end)
                
                episodes_subtitles[found_episode].append({
                    'speaker': subtitle['speaker'],
                    'start_time': format_srt_time(relative_start),
                    'end_time': format_srt_time(relative_end),
                    'text': subtitle['text'],
                    'original_start': subtitle['start_time'],
                    'original_end': subtitle['end_time']
                })
            else:
                print(f"   ⚠️  无法分配字幕到分集: {subtitle['start_time']} - {subtitle['text'][:30]}...")
                
        except Exception as e:
            print(f"   ❌ 处理字幕时出错: {e}")
            continue
    
    # 保存每个分集的字幕文件
    print("💾 保存分集字幕文件:")
    
    for episode_name, subtitles in episodes_subtitles.items():
        if not subtitles:
            print(f"   ⚠️  {episode_name}: 无字幕数据")
            continue
            
        # 按时间排序
        subtitles.sort(key=lambda x: parse_srt_time(x['start_time']))
        
        # 保存为JSON格式（带说话人）
        json_file = output_path / f"{episode_name}_with_speaker.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(subtitles, f, ensure_ascii=False, indent=2)
        
        # 保存为SRT格式（带说话人）
        srt_with_speaker_file = output_path / f"{episode_name}_with_speaker.srt"
        with open(srt_with_speaker_file, 'w', encoding='utf-8') as f:
            for i, subtitle in enumerate(subtitles, 1):
                f.write(f"{i}\n")
                f.write(f"{subtitle['start_time']} --> {subtitle['end_time']}\n")
                f.write(f"[{subtitle['speaker']}] {subtitle['text']}\n\n")
        
        # 保存为标准SRT格式（不带说话人）
        srt_file = output_path / f"{episode_name}.srt"
        with open(srt_file, 'w', encoding='utf-8') as f:
            for i, subtitle in enumerate(subtitles, 1):
                f.write(f"{i}\n")
                f.write(f"{subtitle['start_time']} --> {subtitle['end_time']}\n")
                f.write(f"{subtitle['text']}\n\n")
        
        print(f"   ✅ {episode_name}: {len(subtitles)} 条字幕")
        print(f"      - {json_file.name}")
        print(f"      - {srt_with_speaker_file.name}")
        print(f"      - {srt_file.name}")

def create_summary(subtitle_data: List[Dict], timeline_info: List[Dict], output_dir: str):
    """创建分割总结报告"""
    summary_file = Path(output_dir) / "split_summary.json"
    
    # 统计每个分集的字幕数量
    episode_stats = {}
    total_subtitles = len(subtitle_data)
    
    for episode in timeline_info:
        chapter_name = episode['chapter']
        episode_start = episode['start_time']
        episode_end = episode['end_time']
        
        # 计算这个分集有多少字幕
        episode_subtitle_count = 0
        for subtitle in subtitle_data:
            start_seconds = parse_srt_time(subtitle['start_time'])
            if episode_start <= start_seconds < episode_end:
                episode_subtitle_count += 1
        
        episode_stats[chapter_name] = {
            'start_time': episode_start,
            'end_time': episode_end,
            'duration': episode['duration'],
            'subtitle_count': episode_subtitle_count
        }
    
    summary = {
        'total_episodes': len(timeline_info),
        'total_subtitles': total_subtitles,
        'output_directory': output_dir,
        'episode_statistics': episode_stats,
        'files_created': {
            'json_files': [f"{ep['chapter']}_with_speaker.json" for ep in timeline_info],
            'srt_with_speaker_files': [f"{ep['chapter']}_with_speaker.srt" for ep in timeline_info],
            'srt_files': [f"{ep['chapter']}.srt" for ep in timeline_info]
        }
    }
    
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"\n📊 分割总结:")
    print(f"   • 总分集数: {len(timeline_info)}")
    print(f"   • 总字幕数: {total_subtitles}")
    print(f"   • 输出目录: {output_dir}")
    print(f"   • 总结文件: {summary_file.name}")

def main():
    print("🎬 按分集分割字幕工具")
    print("=" * 50)
    
    # 配置文件路径
    subtitle_file = "subtitle_output_raw_0902.txt"
    timeline_file = "merge_timeline.json"
    output_dir = "episodes_subtitles"
    
    print(f"📄 字幕文件: {subtitle_file}")
    print(f"⏱️  时间轴文件: {timeline_file}")
    print(f"📁 输出目录: {output_dir}")
    print()
    
    # 检查文件存在性
    if not os.path.exists(subtitle_file):
        print(f"❌ 字幕文件不存在: {subtitle_file}")
        return 1
    
    if not os.path.exists(timeline_file):
        print(f"❌ 时间轴文件不存在: {timeline_file}")
        return 1
    
    # 1. 加载时间轴信息
    print("1️⃣ 加载时间轴信息...")
    timeline_info = load_timeline_info(timeline_file)
    if not timeline_info:
        print("❌ 无法加载时间轴信息")
        return 1
    
    print(f"   ✅ 加载了 {len(timeline_info)} 个分集的时间信息")
    
    # 2. 加载字幕数据
    print("\n2️⃣ 加载字幕数据...")
    subtitle_data = load_subtitle_data(subtitle_file)
    if not subtitle_data:
        print("❌ 无法加载字幕数据")
        return 1
    
    print(f"   ✅ 加载了 {len(subtitle_data)} 条字幕")
    
    # 3. 按分集分割字幕
    print("\n3️⃣ 按分集分割字幕...")
    split_subtitles_by_episodes(subtitle_data, timeline_info, output_dir)
    
    # 4. 创建总结报告
    print("\n4️⃣ 创建总结报告...")
    create_summary(subtitle_data, timeline_info, output_dir)
    
    print("\n🎉 分割完成!")
    print(f"所有文件已保存到 {output_dir} 目录中")

if __name__ == "__main__":
    main()
