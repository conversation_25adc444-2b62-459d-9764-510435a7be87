# 音频压缩工具使用说明

这是一个智能音频压缩工具，可以将任意大小的音频文件压缩到指定大小以下（默认10MB）。

## 功能特点

- 🎵 支持多种音频格式输入（WAV, MP3, FLAC, AAC等）
- 📦 智能压缩算法，自动计算最佳参数
- 🎛️ 三种质量模式：高质量、平衡、激进
- 📊 批量处理支持
- 🔧 自动检测FFmpeg依赖

## 安装依赖

### 1. 安装FFmpeg（必需）

**Windows:**
```bash
# 使用chocolatey（推荐）
choco install ffmpeg

# 或者手动下载
# 1. 访问 https://ffmpeg.org/download.html
# 2. 下载Windows版本
# 3. 解压并添加到PATH环境变量
```

**macOS:**
```bash
brew install ffmpeg
```

**Linux:**
```bash
sudo apt update
sudo apt install ffmpeg
```

### 2. 安装Python依赖（可选）
```bash
pip install -r requirements_audio.txt
```

## 使用方法

### 命令行使用

**基本用法：**
```bash
# 压缩单个文件到10MB以下
python audio_compressor.py input.wav

# 指定输出文件名
python audio_compressor.py input.wav -o compressed.mp3

# 自定义目标大小（5MB）
python audio_compressor.py input.wav -s 5.0

# 选择质量模式
python audio_compressor.py input.wav -q high     # 高质量
python audio_compressor.py input.wav -q balanced # 平衡（默认）
python audio_compressor.py input.wav -q aggressive # 激进压缩
```

**批量处理：**
```bash
# 批量处理目录中的所有WAV文件
python audio_compressor.py /path/to/audio/folder --batch -o /path/to/output

# 处理特定格式文件
python audio_compressor.py /path/to/folder --batch -s 8.0
```

### Python代码使用

```python
from audio_compressor import AudioCompressor

# 创建压缩器（目标10MB）
compressor = AudioCompressor(target_size_mb=10.0)

# 压缩单个文件
success = compressor.compress_audio('input.wav', 'output.mp3', quality='balanced')

# 批量压缩
results = compressor.compress_multiple('input_folder', 'output_folder', '*.wav')
```

## 压缩质量说明

| 模式 | 说明 | 适用场景 |
|------|------|----------|
| `high` | 保持较高音质，适度压缩 | 音乐、重要录音 |
| `balanced` | 平衡音质和文件大小 | 一般用途（默认） |
| `aggressive` | 最大压缩，可能损失音质 | 对文件大小要求严格 |

## 压缩原理

工具会根据音频时长和目标文件大小智能计算：

1. **目标比特率**：根据时长和目标大小计算最佳比特率
2. **采样率调整**：在保证质量的前提下适当降低采样率
3. **声道优化**：必要时转换为单声道以减小文件
4. **编码优化**：使用高效的MP3编码器

## 测试功能

运行测试脚本验证工具功能：

```bash
python test_audio_compressor.py
```

测试内容包括：
- 不同大小文件的压缩效果
- 各种质量模式对比
- 真实音频文件压缩测试

## 注意事项

1. **FFmpeg必需**：工具依赖FFmpeg进行音频处理
2. **格式支持**：输出格式固定为MP3（兼容性最好）
3. **质量权衡**：极小的目标大小可能显著影响音质
4. **处理时间**：大文件处理需要一定时间

## 故障排除

**FFmpeg未找到：**
- 确保FFmpeg已安装并在PATH中
- Windows用户检查环境变量设置

**压缩失败：**
- 检查输入文件是否损坏
- 确认有足够的磁盘空间
- 尝试使用aggressive模式

**文件过大：**
- 降低目标大小设置
- 使用aggressive质量模式
- 考虑分割长音频文件
