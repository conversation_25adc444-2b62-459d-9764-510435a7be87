#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
选择每个角色目录下时长最长的音频文件
并将它们归档到一个新文件夹中
"""

import os
import shutil
import subprocess
from pathlib import Path
import json

def get_audio_duration(audio_file):
    """
    使用FFprobe获取音频文件的时长（秒）
    """
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            audio_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                               encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            duration = float(data['format']['duration'])
            return duration
        else:
            print(f"   ⚠️  无法获取时长: {os.path.basename(audio_file)}")
            return 0.0
            
    except Exception as e:
        print(f"   ❌ 获取时长失败: {e}")
        return 0.0

def format_duration(seconds):
    """将秒数格式化为 MM:SS 格式"""
    minutes = int(seconds // 60)
    secs = int(seconds % 60)
    return f"{minutes:02d}:{secs:02d}"

def find_longest_audio_in_directory(directory):
    """
    在指定目录中找到时长最长的音频文件
    返回 (文件路径, 时长)
    """
    audio_files = list(Path(directory).glob("*.wav"))
    
    if not audio_files:
        return None, 0.0
    
    longest_file = None
    max_duration = 0.0
    
    print(f"   🔍 分析 {len(audio_files)} 个音频文件...")
    
    for audio_file in audio_files:
        duration = get_audio_duration(str(audio_file))
        
        if duration > max_duration:
            max_duration = duration
            longest_file = audio_file
        
        print(f"      📄 {audio_file.name}: {format_duration(duration)}")
    
    return longest_file, max_duration

def main():
    print("🎵 选择最长音频文件工具")
    print("=" * 60)
    
    # 配置
    source_dir = "chapter_002_extracted_voices"
    archive_dir = "chapter_002_longest_voices"
    
    print(f"📁 源目录: {source_dir}")
    print(f"📁 归档目录: {archive_dir}")
    print()
    
    # 检查源目录
    if not os.path.exists(source_dir):
        print(f"❌ 源目录不存在: {source_dir}")
        return 1
    
    # 检查FFprobe
    try:
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
        print("✅ FFprobe检测成功")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ FFprobe未安装，请确保安装了完整的FFmpeg套件")
        return 1
    
    # 创建归档目录
    archive_path = Path(archive_dir)
    archive_path.mkdir(exist_ok=True)
    print(f"✅ 归档目录已创建: {archive_dir}")
    print()
    
    # 扫描角色目录
    results = {}
    total_duration = 0.0
    
    print("🎤 处理角色目录:")
    
    for item in Path(source_dir).iterdir():
        if item.is_dir() and item.name != '__pycache__':
            character_name = item.name
            print(f"\n   🎭 角色: {character_name}")
            
            # 找到最长的音频文件
            longest_file, duration = find_longest_audio_in_directory(item)
            
            if longest_file:
                # 生成新的文件名（包含角色名和时长）
                duration_str = format_duration(duration)
                new_filename = f"{character_name}_longest_{duration_str.replace(':', '-')}.wav"
                target_path = archive_path / new_filename
                
                # 复制文件
                try:
                    shutil.copy2(longest_file, target_path)
                    file_size = target_path.stat().st_size / 1024  # KB
                    
                    results[character_name] = {
                        "original_file": str(longest_file),
                        "archived_file": str(target_path),
                        "duration": duration,
                        "duration_formatted": duration_str,
                        "file_size_kb": round(file_size, 1)
                    }
                    
                    total_duration += duration
                    
                    print(f"   ✅ 最长音频: {longest_file.name}")
                    print(f"      时长: {duration_str}")
                    print(f"      大小: {file_size:.1f} KB")
                    print(f"      已复制到: {new_filename}")
                    
                except Exception as e:
                    print(f"   ❌ 复制失败: {e}")
            else:
                print(f"   ⚠️  未找到音频文件")
    
    # 生成归档摘要
    summary = {
        "archive_info": {
            "date": "2024-12-26",
            "source_directory": source_dir,
            "archive_directory": archive_dir,
            "total_characters": len(results),
            "total_duration": total_duration,
            "total_duration_formatted": format_duration(total_duration)
        },
        "longest_audio_files": results
    }
    
    summary_file = archive_path / "longest_voices_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    # 显示最终结果
    print(f"\n🎉 归档完成！")
    print("=" * 60)
    
    print(f"📊 归档统计:")
    print(f"   🎭 角色数量: {len(results)}")
    print(f"   ⏱️  总时长: {format_duration(total_duration)}")
    print(f"   📁 归档目录: {archive_dir}")
    print(f"   📝 摘要文件: {summary_file}")
    
    print(f"\n📋 归档详情:")
    for character, info in results.items():
        print(f"   🎤 {character}:")
        print(f"      📄 文件: {os.path.basename(info['archived_file'])}")
        print(f"      ⏱️  时长: {info['duration_formatted']}")
        print(f"      💾 大小: {info['file_size_kb']} KB")
    
    print(f"\n📁 归档目录结构:")
    print(f"📁 {archive_dir}/")
    for character in results.keys():
        archived_file = results[character]['archived_file']
        filename = os.path.basename(archived_file)
        duration = results[character]['duration_formatted']
        print(f"   ├── 🎵 {filename}")
    print(f"   └── 📝 longest_voices_summary.json")
    
    return 0

if __name__ == "__main__":
    exit(main())
