#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gemini Audio SRT API 使用示例
"""

import requests
import json

# API 基础URL
API_BASE_URL = "http://218.206.102.132:9030"

def test_api():
    """测试API功能"""
    
    # 读取SRT文件
    srt_file_path = "归档/chapter_002/ori.srt"
    try:
        with open(srt_file_path, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        print(f"成功读取SRT文件: {srt_file_path}")
    except FileNotFoundError:
        print(f"文件未找到: {srt_file_path}")
        srt_content = ""
    except Exception as e:
        print(f"读取文件时出错: {e}")
        srt_content = ""
    
    # 示例数据
    test_data = {
        "file_url": "https://gidfyoqecthbormpbirt.supabase.co/storage/v1/object/public/audios/ori_audio_compressed.mp3",
        "srt_file": srt_content,
        # prompt 参数是可选的，不提供则使用默认值
    }
    
    try:
        # 发送POST请求
        response = requests.post(
            f"{API_BASE_URL}/process-audio-srt",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            print("API调用成功!")
            print("状态:", result["status"])
            print("输出结果:")
            print(result["output"])
            
            # 保存输出结果到txt文件
            output_file = "api_output_result.txt"
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write("=== Gemini Audio SRT API 处理结果 ===\n")
                    f.write(f"处理时间: {result.get('timestamp', 'N/A')}\n")
                    f.write(f"状态: {result['status']}\n")
                    f.write("=" * 50 + "\n")
                    f.write("输出结果:\n")
                    f.write(result["output"])
                    f.write("\n")
                print(f"结果已保存到文件: {output_file}")
            except Exception as e:
                print(f"保存文件时出错: {e}")
        else:
            print(f"API调用失败，状态码: {response.status_code}")
            print("错误信息:", response.text)
            
            # 保存错误信息到txt文件
            error_file = "api_error_result.txt"
            try:
                with open(error_file, 'w', encoding='utf-8') as f:
                    f.write("=== Gemini Audio SRT API 错误信息 ===\n")
                    f.write(f"状态码: {response.status_code}\n")
                    f.write("=" * 50 + "\n")
                    f.write("错误详情:\n")
                    f.write(response.text)
                    f.write("\n")
                print(f"错误信息已保存到文件: {error_file}")
            except Exception as e:
                print(f"保存错误文件时出错: {e}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

def test_health_check():
    """测试健康检查端点"""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            print("健康检查通过:", response.json())
        else:
            print(f"健康检查失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"健康检查异常: {e}")

if __name__ == "__main__":
    print("=== Gemini Audio SRT API 测试 ===")
    
    # 先测试健康检查
    print("\n1. 测试健康检查...")
    test_health_check()
    
    # 测试主要功能
    print("\n2. 测试音频SRT处理...")
    test_api()
