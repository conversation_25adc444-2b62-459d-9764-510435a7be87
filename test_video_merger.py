#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频字幕合成工具测试脚本
"""

import os
import sys
from video_subtitle_merger import VideoSubtitleMerger

def test_single_file():
    """测试单文件合成"""
    print("=== 测试单文件合成 ===")
    
    # 使用您项目中的实际文件
    video_path = "归档/chapter_011/ori_video.mp4"
    subtitle_path = "split_srt/chapter_011_with_speaker.srt"
    output_path = "test_output/chapter_011_test.mp4"
    
    # 检查输入文件是否存在
    if not os.path.exists(video_path):
        print(f"❌ 测试视频文件不存在: {video_path}")
        return False
    
    if not os.path.exists(subtitle_path):
        print(f"❌ 测试字幕文件不存在: {subtitle_path}")
        return False
    
    # 创建输出目录
    os.makedirs("test_output", exist_ok=True)
    
    # 创建合成器实例
    try:
        merger = VideoSubtitleMerger()
        print("✅ VideoSubtitleMerger 初始化成功")
    except Exception as e:
        print(f"❌ VideoSubtitleMerger 初始化失败: {e}")
        return False
    
    # 执行合成
    print(f"开始测试合成...")
    print(f"视频文件: {video_path}")
    print(f"字幕文件: {subtitle_path}")
    print(f"输出文件: {output_path}")
    
    success = merger.merge_subtitle(
        video_path=video_path,
        subtitle_path=subtitle_path,
        output_path=output_path,
        font_size=22,
        font_color='white',
        outline_color='black',
        outline_width=2,
        position='bottom'
    )
    
    if success:
        print("✅ 单文件合成测试通过")
        return True
    else:
        print("❌ 单文件合成测试失败")
        return False

def test_batch_processing():
    """测试批量处理功能"""
    print("\n=== 测试批量处理功能 ===")
    
    # 创建测试目录结构
    test_dir = "test_batch"
    os.makedirs(test_dir, exist_ok=True)
    
    # 检查是否有可用的测试文件
    available_chapters = []
    for i in range(2, 12):
        chapter_name = f"chapter_{i:03d}"
        video_path = f"归档/{chapter_name}/ori_video.mp4"
        subtitle_path = f"split_srt/{chapter_name}_with_speaker.srt"
        
        if os.path.exists(video_path) and os.path.exists(subtitle_path):
            available_chapters.append(chapter_name)
    
    if not available_chapters:
        print("❌ 没有找到可用的测试文件进行批量处理测试")
        return False
    
    print(f"找到 {len(available_chapters)} 个可用章节: {available_chapters[:3]}...")
    
    # 只测试前3个章节以节省时间
    test_chapters = available_chapters[:3]
    
    # 复制测试文件到测试目录
    import shutil
    for chapter in test_chapters:
        video_src = f"归档/{chapter}/ori_video.mp4"
        subtitle_src = f"split_srt/{chapter}_with_speaker.srt"
        
        shutil.copy2(video_src, os.path.join(test_dir, f"{chapter}.mp4"))
        shutil.copy2(subtitle_src, os.path.join(test_dir, f"{chapter}.srt"))
    
    # 执行批量处理
    try:
        merger = VideoSubtitleMerger()
        output_dir = os.path.join(test_dir, "output")
        count = merger.batch_merge(test_dir, output_dir)
        
        if count > 0:
            print(f"✅ 批量处理测试通过，成功处理 {count} 个文件")
            return True
        else:
            print("❌ 批量处理测试失败，没有成功处理任何文件")
            return False
            
    except Exception as e:
        print(f"❌ 批量处理测试出现异常: {e}")
        return False

def test_file_validation():
    """测试文件验证功能"""
    print("\n=== 测试文件验证功能 ===")
    
    merger = VideoSubtitleMerger()
    
    # 测试不存在的文件
    is_valid, message = merger.validate_files("nonexistent.mp4", "nonexistent.srt")
    if not is_valid and "不存在" in message:
        print("✅ 不存在文件检测正常")
    else:
        print("❌ 不存在文件检测异常")
        return False
    
    # 测试存在的文件
    video_path = "归档/chapter_011/ori_video.mp4"
    subtitle_path = "split_srt/chapter_011_with_speaker.srt"
    
    if os.path.exists(video_path) and os.path.exists(subtitle_path):
        is_valid, message = merger.validate_files(video_path, subtitle_path)
        if is_valid:
            print("✅ 有效文件检测正常")
        else:
            print(f"❌ 有效文件检测异常: {message}")
            return False
    else:
        print("⚠️  跳过有效文件检测（测试文件不存在）")
    
    print("✅ 文件验证功能测试通过")
    return True

def cleanup_test_files():
    """清理测试文件"""
    print("\n=== 清理测试文件 ===")
    
    import shutil
    
    # 删除测试目录
    test_dirs = ["test_output", "test_batch"]
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            try:
                shutil.rmtree(test_dir)
                print(f"✅ 已删除测试目录: {test_dir}")
            except Exception as e:
                print(f"⚠️  删除测试目录失败: {test_dir} - {e}")

def main():
    """主测试函数"""
    print("🚀 开始视频字幕合成工具测试\n")
    
    test_results = []
    
    # 运行各项测试
    try:
        # 测试文件验证
        result1 = test_file_validation()
        test_results.append(("文件验证功能", result1))
        
        # 测试单文件合成（可选，因为比较耗时）
        user_input = input("\n是否进行单文件合成测试？这可能需要几分钟时间 (y/N): ").strip().lower()
        if user_input in ['y', 'yes']:
            result2 = test_single_file()
            test_results.append(("单文件合成", result2))
        
        # 测试批量处理（可选）
        user_input = input("\n是否进行批量处理测试？这可能需要较长时间 (y/N): ").strip().lower()
        if user_input in ['y', 'yes']:
            result3 = test_batch_processing()
            test_results.append(("批量处理", result3))
        
    except KeyboardInterrupt:
        print("\n用户取消测试")
    except Exception as e:
        print(f"\n测试过程中出现异常: {e}")
    finally:
        # 清理测试文件
        cleanup_test_files()
    
    # 输出测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1
    
    total_tests = len(test_results)
    if total_tests > 0:
        print(f"\n总计: {passed_count}/{total_tests} 项测试通过")
        
        if passed_count == total_tests:
            print("🎉 所有测试都通过了！工具可以正常使用。")
        else:
            print("⚠️  部分测试失败，请检查相关功能。")
    else:
        print("没有执行任何测试")

if __name__ == '__main__':
    main()


