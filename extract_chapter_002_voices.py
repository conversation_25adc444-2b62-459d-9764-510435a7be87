#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Chapter 002 角色语音提取脚本
自动修复时间格式并提取不同说话者的音频片段
"""

import os
import json
import subprocess
import sys
from pathlib import Path

def check_ffmpeg():
    """检查FFmpeg是否已安装"""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True, 
                      encoding='utf-8', errors='ignore')
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        return False

def fix_time_format(subtitle_data):
    """将时间格式从 MM:SS.mmm 转换为 HH:MM:SS.mmm"""
    fixed_data = []
    for item in subtitle_data:
        new_item = item.copy()
        
        # 处理start_time
        start_time = item['start_time']
        if start_time.count(':') == 1:  # MM:SS.mmm格式
            new_item['start_time'] = '00:' + start_time
        
        # 处理end_time
        end_time = item['end_time']
        if end_time.count(':') == 1:  # MM:SS.mmm格式
            new_item['end_time'] = '00:' + end_time
        
        fixed_data.append(new_item)
    
    return fixed_data

def extract_audio_segment(input_file, output_file, start_time, duration):
    """使用FFmpeg提取音频片段"""
    try:
        cmd = [
            'ffmpeg',
            '-i', input_file,
            '-ss', str(start_time),
            '-t', str(duration),
            '-acodec', 'pcm_s16le',  # WAV格式
            '-ar', '44100',          # 采样率
            '-ac', '2',              # 立体声
            '-y',                    # 覆盖输出文件
            output_file
        ]
        
        # 修复编码问题：指定UTF-8编码并忽略错误
        result = subprocess.run(cmd, capture_output=True, text=True, 
                               encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            return os.path.exists(output_file) and os.path.getsize(output_file) > 0
        else:
            # 只在有实际错误时才显示，忽略编码错误
            if result.stderr and result.stderr.strip():
                print(f"   ⚠️  FFmpeg警告: {result.stderr[:100]}...")
            return False
            
    except Exception as e:
        print(f"   ❌ 提取失败: {e}")
        return False

def parse_time_to_seconds(time_str):
    """将HH:MM:SS.mmm格式转换为秒数"""
    try:
        parts = time_str.split(':')
        hours = int(parts[0])
        minutes = int(parts[1])
        
        sec_parts = parts[2].split('.')
        seconds = int(sec_parts[0])
        milliseconds = int(sec_parts[1]) if len(sec_parts) > 1 else 0
        
        return hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
    except Exception as e:
        print(f"时间解析错误 '{time_str}': {e}")
        raise

def main():
    print("🎬 Chapter 002 角色语音提取工具")
    print("=" * 60)
    
    # 文件配置
    subtitle_file = "subtitle_output_chapter_002_20250902_100728.json"
    audio_file = "归档/chapter_002/ori_audio.wav"
    output_dir = "chapter_002_extracted_voices"
    
    print(f"📝 字幕文件: {subtitle_file}")
    print(f"🎵 音频文件: {audio_file}")
    print(f"📁 输出目录: {output_dir}")
    print()
    
    # 1. 检查前置条件
    print("1️⃣ 检查前置条件...")
    
    if not check_ffmpeg():
        print("❌ FFmpeg未安装，请先安装FFmpeg")
        print("   下载地址: https://ffmpeg.org/download.html")
        return 1
    print("   ✅ FFmpeg已安装")
    
    if not os.path.exists(subtitle_file):
        print(f"   ❌ 字幕文件不存在: {subtitle_file}")
        return 1
    print(f"   ✅ 字幕文件存在")
    
    if not os.path.exists(audio_file):
        print(f"   ❌ 音频文件不存在: {audio_file}")
        return 1
    print(f"   ✅ 音频文件存在")
    
    # 2. 加载和处理字幕数据
    print("\n2️⃣ 加载字幕数据...")
    try:
        with open(subtitle_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        print(f"   ✅ 加载了 {len(original_data)} 条字幕记录")
        
        # 修复时间格式
        fixed_data = fix_time_format(original_data)
        print("   ✅ 时间格式已修复")
        
        # 分析角色信息
        characters = {}
        for item in fixed_data:
            speaker = item.get('speaker', '未知')
            if speaker not in characters:
                characters[speaker] = []
            characters[speaker].append(item)
        
        print(f"   📊 发现 {len(characters)} 个角色:")
        for speaker, items in characters.items():
            print(f"      🎤 {speaker}: {len(items)} 句对话")
            
    except Exception as e:
        print(f"   ❌ 加载字幕失败: {e}")
        return 1
    
    # 3. 创建输出目录
    print("\n3️⃣ 创建输出目录...")
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    print(f"   ✅ 输出目录: {output_dir}")
    
    # 4. 提取语音片段
    print("\n4️⃣ 开始提取语音片段...")
    total_extracted = 0
    results = {}
    
    for speaker, items in characters.items():
        print(f"\n   🎤 处理角色: {speaker}")
        
        # 创建角色专用目录
        speaker_dir = output_path / speaker
        speaker_dir.mkdir(exist_ok=True)
        
        speaker_files = []
        
        for i, item in enumerate(items, 1):
            try:
                # 解析时间
                start_seconds = parse_time_to_seconds(item['start_time'])
                end_seconds = parse_time_to_seconds(item['end_time'])
                duration = end_seconds - start_seconds
                
                if duration <= 0:
                    print(f"      ⚠️  跳过无效片段: {item['start_time']} - {item['end_time']}")
                    continue
                
                # 生成文件名 - 使用原始时间格式的清理版本
                original_start = item['start_time'].replace('00:', '').replace(':', '-').replace('.', '-')
                output_file = speaker_dir / f"{speaker}_{i:03d}_{original_start}.wav"
                
                # 提取音频
                print(f"      🔄 提取: {item['start_time']} - {item['end_time']} ({duration:.2f}秒)")
                success = extract_audio_segment(
                    audio_file, str(output_file), start_seconds, duration
                )
                
                if success:
                    speaker_files.append(str(output_file))
                    file_size = os.path.getsize(output_file) / 1024  # KB
                    total_extracted += 1
                    
                    print(f"      ✅ {output_file.name} ({file_size:.1f} KB)")
                    print(f"         内容: {item['text']}")
                else:
                    print(f"      ❌ 提取失败: {output_file.name}")
                    
            except Exception as e:
                print(f"      ❌ 处理失败: {e}")
                continue
        
        results[speaker] = speaker_files
        print(f"   📊 {speaker} 完成: {len(speaker_files)} 个文件")
    
    # 5. 生成摘要报告
    print("\n5️⃣ 生成摘要报告...")
    summary = {
        "extraction_info": {
            "date": "2024-12-26",
            "source_audio": audio_file,
            "source_subtitle": subtitle_file,
            "output_directory": output_dir,
            "total_segments": total_extracted
        },
        "characters": {}
    }
    
    for speaker, files in results.items():
        total_size = sum(os.path.getsize(f) for f in files if os.path.exists(f))
        summary["characters"][speaker] = {
            "segment_count": len(files),
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "files": [os.path.basename(f) for f in files]
        }
    
    summary_file = output_path / "extraction_summary.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ 摘要已保存: {summary_file}")
    
    # 6. 显示最终结果
    print("\n🎉 提取完成！")
    print("=" * 60)
    
    total_size_all = 0
    for speaker, files in results.items():
        file_count = len(files)
        total_size = sum(os.path.getsize(f) for f in files if os.path.exists(f))
        total_size_mb = total_size / (1024 * 1024)
        total_size_all += total_size
        
        print(f"🎤 {speaker}:")
        print(f"   📁 文件数量: {file_count}")
        print(f"   💾 总大小: {total_size_mb:.2f} MB")
        
        # 显示前几个文件
        speaker_dir = output_path / speaker
        if speaker_dir.exists():
            wav_files = list(speaker_dir.glob("*.wav"))
            for wav_file in wav_files[:3]:  # 只显示前3个
                size_kb = wav_file.stat().st_size / 1024
                print(f"   📄 {wav_file.name} ({size_kb:.1f} KB)")
            if len(wav_files) > 3:
                print(f"   ... 还有 {len(wav_files) - 3} 个文件")
        print()
    
    total_size_all_mb = total_size_all / (1024 * 1024)
    print(f"📊 总计: {total_extracted} 个音频文件")
    print(f"💾 总大小: {total_size_all_mb:.2f} MB")
    print(f"📁 输出目录: {output_dir}")
    print(f"📝 摘要文件: {summary_file}")
    
    return 0

if __name__ == "__main__":
    exit(main())
