#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
音频拼接工具
先拼接chapter_003原音频，然后在后面按顺序添加chapter_002_longest_voices中的音频
生成详细的时间轴信息
"""

import os
import json
import subprocess
from pathlib import Path
from datetime import timedelta

def get_audio_duration(audio_file):
    """获取音频文件时长（秒）"""
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            str(audio_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                               encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            return float(data['format']['duration'])
        else:
            print(f"   ⚠️  无法获取时长: {audio_file}")
            return 0.0
            
    except Exception as e:
        print(f"   ❌ 获取时长失败: {e}")
        return 0.0

def format_duration(seconds):
    """将秒数格式化为 HH:MM:SS.mmm 格式"""
    td = timedelta(seconds=seconds)
    total_seconds = int(td.total_seconds())
    hours, remainder = divmod(total_seconds, 3600)
    minutes, secs = divmod(remainder, 60)
    milliseconds = int((seconds - total_seconds) * 1000)
    return f"{hours:02d}:{minutes:02d}:{secs:02d}.{milliseconds:03d}"

def concatenate_audio_files(audio_files, output_file):
    """使用FFmpeg拼接音频文件"""
    try:
        # 创建临时文件列表
        filelist_path = "temp_filelist.txt"
        
        with open(filelist_path, 'w', encoding='utf-8') as f:
            for audio_file in audio_files:
                # FFmpeg要求路径用单引号包围，并转义特殊字符
                escaped_path = str(audio_file).replace("'", "'\"'\"'")
                f.write(f"file '{escaped_path}'\n")
        
        # FFmpeg拼接命令
        cmd = [
            'ffmpeg',
            '-f', 'concat',
            '-safe', '0',
            '-i', filelist_path,
            '-c', 'copy',
            '-y',
            str(output_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True,
                               encoding='utf-8', errors='ignore')
        
        # 清理临时文件
        if os.path.exists(filelist_path):
            os.remove(filelist_path)
        
        if result.returncode == 0:
            return True
        else:
            print(f"❌ FFmpeg拼接失败:")
            print(f"   {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 拼接失败: {e}")
        return False

def main():
    print("🎵 音频拼接与时间轴生成工具（原音频在前）")
    print("=" * 70)
    
    # 配置文件路径
    chapter_002_dir = "chapter_002_longest_voices"
    chapter_003_audio = "归档/chapter_003/ori_audio.wav"
    output_file = "chapter003_plus_chapter002_voices.wav"
    timeline_file = "audio_timeline.json"
    
    print(f"🎵 Chapter 003 原音频: {chapter_003_audio}")
    print(f"📁 Chapter 002 最长音频目录: {chapter_002_dir}")
    print(f"📄 输出文件: {output_file}")
    print(f"⏱️  时间轴文件: {timeline_file}")
    print()
    
    # 检查文件存在性
    if not os.path.exists(chapter_002_dir):
        print(f"❌ Chapter 002 目录不存在: {chapter_002_dir}")
        return 1
    
    if not os.path.exists(chapter_003_audio):
        print(f"❌ Chapter 003 音频不存在: {chapter_003_audio}")
        return 1
    
    # 检查FFmpeg和FFprobe
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
        print("✅ FFmpeg和FFprobe检测成功")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 请确保安装了完整的FFmpeg套件")
        return 1
    
    # 1. 首先添加Chapter 003原音频
    print("\n1️⃣ 分析Chapter 003原音频...")
    
    timeline_info = []
    current_time = 0.0
    
    chapter_003_duration = get_audio_duration(chapter_003_audio)
    
    timeline_info.append({
        "segment": "Chapter_003_Original",
        "file": chapter_003_audio,
        "start_time": current_time,
        "end_time": current_time + chapter_003_duration,
        "duration": chapter_003_duration,
        "start_formatted": format_duration(current_time),
        "end_formatted": format_duration(current_time + chapter_003_duration),
        "duration_formatted": format_duration(chapter_003_duration)
    })
    
    print(f"   🎵 Chapter 003: {os.path.basename(chapter_003_audio)}")
    print(f"      时间: {format_duration(current_time)} - {format_duration(current_time + chapter_003_duration)}")
    print(f"      时长: {format_duration(chapter_003_duration)}")
    
    current_time += chapter_003_duration
    
    # 2. 读取Chapter 002最长音频摘要
    print("\n2️⃣ 分析Chapter 002最长音频...")
    
    summary_file = Path(chapter_002_dir) / "longest_voices_summary.json"
    if summary_file.exists():
        with open(summary_file, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        print("   ✅ 读取摘要成功")
    else:
        print("   ⚠️  摘要文件不存在，将直接扫描目录")
        summary = None
    
    # 3. 按照指定顺序收集Chapter 002音频文件
    print("\n3️⃣ 添加Chapter 002音频文件...")
    
    # 定义添加顺序：孙媳妇 -> 路人1 -> 路人2 -> 路人3
    character_order = ["孙媳妇", "路人1", "路人2", "路人3"]
    
    chapter_002_files = []
    
    for character in character_order:
        # 查找对应的音频文件
        pattern = f"{character}_longest_*.wav"
        matching_files = list(Path(chapter_002_dir).glob(pattern))
        
        if matching_files:
            audio_file = matching_files[0]  # 取第一个匹配的文件
            duration = get_audio_duration(audio_file)
            
            chapter_002_files.append(audio_file)
            
            timeline_info.append({
                "segment": f"Chapter_002_{character}",
                "file": str(audio_file),
                "start_time": current_time,
                "end_time": current_time + duration,
                "duration": duration,
                "start_formatted": format_duration(current_time),
                "end_formatted": format_duration(current_time + duration),
                "duration_formatted": format_duration(duration)
            })
            
            print(f"   🎤 {character}: {audio_file.name}")
            print(f"      时间: {format_duration(current_time)} - {format_duration(current_time + duration)}")
            print(f"      时长: {format_duration(duration)}")
            
            current_time += duration
        else:
            print(f"   ⚠️  未找到角色音频: {character}")
    
    total_duration = current_time
    
    # 4. 执行音频拼接（Chapter 003在前，Chapter 002在后）
    print(f"\n4️⃣ 开始音频拼接...")
    
    all_files = [Path(chapter_003_audio)] + chapter_002_files
    
    print(f"   📋 拼接顺序:")
    for i, file in enumerate(all_files, 1):
        segment_type = "原音频" if i == 1 else f"角色音频"
        print(f"      {i}. {file.name} ({segment_type})")
    
    success = concatenate_audio_files(all_files, output_file)
    
    if success:
        output_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
        print(f"   ✅ 拼接成功!")
        print(f"   📄 输出文件: {output_file}")
        print(f"   💾 文件大小: {output_size:.2f} MB")
        print(f"   ⏱️  总时长: {format_duration(total_duration)}")
    else:
        print(f"   ❌ 拼接失败")
        return 1
    
    # 5. 生成时间轴文件
    print(f"\n5️⃣ 生成时间轴文件...")
    
    timeline_data = {
        "concatenation_info": {
            "date": "2024-12-26",
            "output_file": output_file,
            "total_segments": len(timeline_info),
            "total_duration": total_duration,
            "total_duration_formatted": format_duration(total_duration),
            "structure": "Chapter 003 original audio + Chapter 002 character voices"
        },
        "timeline": timeline_info
    }
    
    with open(timeline_file, 'w', encoding='utf-8') as f:
        json.dump(timeline_data, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ 时间轴已保存: {timeline_file}")
    
    # 6. 显示详细时间轴
    print(f"\n🎉 拼接完成！详细时间轴:")
    print("=" * 70)
    
    for i, segment in enumerate(timeline_info, 1):
        segment_type = "🎵 原音频" if "Original" in segment['segment'] else "🎤 角色音频"
        print(f"{i}. {segment['segment']} {segment_type}")
        print(f"   📄 文件: {os.path.basename(segment['file'])}")
        print(f"   ⏱️  时间: {segment['start_formatted']} → {segment['end_formatted']}")
        print(f"   ⏳ 时长: {segment['duration_formatted']}")
        print()
    
    print(f"📊 总计:")
    print(f"   🎵 音频段数: {len(timeline_info)}")
    print(f"   ⏱️  总时长: {format_duration(total_duration)}")
    print(f"   📄 输出文件: {output_file}")
    print(f"   📋 时间轴: {timeline_file}")
    
    # 7. 创建简化的SRT格式时间轴
    print(f"\n6️⃣ 生成SRT格式时间轴...")
    
    srt_file = "audio_timeline.srt"
    with open(srt_file, 'w', encoding='utf-8') as f:
        for i, segment in enumerate(timeline_info, 1):
            start_srt = segment['start_formatted'].replace('.', ',')
            end_srt = segment['end_formatted'].replace('.', ',')
            
            f.write(f"{i}\n")
            f.write(f"{start_srt} --> {end_srt}\n")
            f.write(f"{segment['segment']}\n")
            f.write(f"{os.path.basename(segment['file'])}\n\n")
    
    print(f"   ✅ SRT时间轴已保存: {srt_file}")
    
    # 8. 显示拼接结构总结
    print(f"\n📋 拼接结构总结:")
    print("=" * 50)
    chapter_003_time = timeline_info[0]['duration_formatted']
    chapter_002_total = sum(item['duration'] for item in timeline_info[1:])
    
    print(f"🎵 Chapter 003 原音频: 00:00:00.000 - {chapter_003_time}")
    print(f"🎤 Chapter 002 角色音频: {chapter_003_time} - {format_duration(total_duration)}")
    print(f"   └── 包含 {len(timeline_info)-1} 个角色音频片段")
    print(f"   └── 总时长: {format_duration(chapter_002_total)}")
    print(f"📊 总文件时长: {format_duration(total_duration)}")
    
    return 0

if __name__ == "__main__":
    exit(main())
