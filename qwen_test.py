import os
from openai import OpenAI
import base64

client = OpenAI(
    # 若没有配置环境变量，请用阿里云百炼API Key将下行替换为：api_key="sk-xxx",
    api_key="sk-fd92079f4c124d6288a9dc143541a451",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

with open('归档/chapter_002/matched_subtitles.txt', 'r', encoding='utf-8') as f:
    matched_subtitles = f.read()

prompt = """
# 音频字幕纠错与补全任务

## 任务目标
基于提供的音频内容，对字幕文本进行精确纠错和补全，识别准确的人名或角色名，输出标准化的JSON格式结果。

## 具体要求

### 1. 纠错内容
- **发言人识别**：准确识别并标注每段台词的发言人（人名或角色名），不要只输出speaker，要输出人名或角色名。
- **台词内容**：修正转录错误、语法问题和表达不准确之处
- **遗漏补充**：识别并补充音频中存在但字幕中缺失的台词内容

### 2. 输出格式
- 采用JSON格式，结构类似SRT字幕格式
- 包含：序号、时间轴、发言人、台词内容

### 3. 时间轴处理规则
- **严格保持**：不得修改原有字幕的时间轴信息
- **合理插入**：补充台词时，确保新增时间轴与前后台词时间逻辑一致
- **无缝衔接**：保证整体时间轴的连续性和准确性

### 4. 质量标准
- 台词与音频内容完全匹配
- 发言人标注准确无误
- 补充内容自然融入原字幕结构
- 时间轴精确对应音频节点

## 输入数据
原字幕文本：{matched_subtitles}

## 输出要求
请严格按照上述要求输出优化后的JSON格式字幕数据。
"""
completion = client.chat.completions.create(
    model="qwen-omni-turbo-latest",
    messages=[
        {
            "role": "user",
            "content": [
                {
                    "type": "input_audio",
                    "input_audio": {
                        "data": "https://gidfyoqecthbormpbirt.supabase.co/storage/v1/object/public/audios/ori_audio_compressed.mp3",
                        "format": "mp3",
                    },
                },
                {"type": "text", "text": f'根据提供的音频，对提供的字幕文本发言人和台词进行纠错,输出结果是json格式，和srt字幕相似。发言人对应人名或角色，如果有漏掉未识别的台词请补充，并保证补充的台词能够正确插入台词字幕中。\n原字幕文本：\n{matched_subtitles}。不要修改字幕文本的时间轴，但是可以在保证时间轴正确的情况下，添加未识别的台词。'},
            ],
        },
    ],
    # 设置输出数据的模态，当前支持两种：["text","audio"]、["text"]
    modalities=["text"],
    # stream 必须设置为 True，否则会报错
    stream=True,
    stream_options={"include_usage": True},
)

# 收集所有流式输出的内容
complete_content = ""
usage_info = None

for chunk in completion:
    print(chunk)  # 保留原有的chunk打印
    
    # 提取内容
    if chunk.choices and len(chunk.choices) > 0:
        delta = chunk.choices[0].delta
        if delta.content:
            complete_content += delta.content
    
    # 提取使用信息（通常在最后一个chunk中）
    if hasattr(chunk, 'usage') and chunk.usage:
        usage_info = chunk.usage

print("\n" + "="*50)
print("完整的拼接结果:")
print("="*50)
print(complete_content)
