# 音频字幕处理器

一个用于合并音频文件和字幕，并使用Google Gemini进行角色识别的Python工具。

## 功能特性

- 🎵 **音频合并**: 自动合并多个章节的音频文件
- 📄 **字幕合并**: 合并对应的SRT字幕文件，自动调整时间轴
- 🤖 **AI角色识别**: 使用Google Gemini AI识别说话人和性别
- 👥 **说话人标注**: 生成带有说话人信息的字幕文件
- 🔍 **质量检查**: 自动比对原始字幕和AI分析结果

## 系统要求

### 必需软件
- Python 3.8+
- FFmpeg (完整版本)
- Google Cloud SDK (已配置Vertex AI权限)

### Python依赖
```bash
pip install google-cloud-aiplatform
```

## 项目结构

```
project/
├── 归档/                          # 音频文件目录
│   ├── chapter_1/
│   │   ├── ori_audio.wav         # 原始音频文件
│   │   └── ori.srt               # 原始字幕文件
│   ├── chapter_2/
│   │   ├── ori_audio.wav
│   │   └── ori.srt
│   └── ...
├── audio_subtitle_processor.py    # 主处理程序
└── README.md                      # 说明文档
```

## 使用方法

### 基本使用

```python
from audio_subtitle_processor import AudioSubtitleProcessor

# 创建处理器实例
processor = AudioSubtitleProcessor(archive_dir="归档")

# 处理所有步骤
audio_url = "https://example.com/your-audio-file.mp3"
results = processor.process_all(audio_url)

# 检查结果
if 'error' in results:
    print(f"处理失败: {results['error']}")
else:
    print("处理成功!")
    for key, value in results.items():
        print(f"{key}: {value}")
```

### 命令行使用

```bash
python audio_subtitle_processor.py
```

## 核心类和方法

### AudioSubtitleProcessor

主要的处理器类，包含以下核心方法：

#### `__init__(archive_dir: str = "归档")`
初始化处理器，指定音频文件存放目录。

#### `get_chapter_folders() -> List[str]`
获取所有章节文件夹列表，按章节号排序。

#### `concatenate_audio_files(audio_files: List[Path], output_file: str) -> bool`
合并多个音频文件为单个文件。

#### `merge_subtitles(subtitle_files: List[Tuple[str, float]], output_file: str) -> bool`
合并多个SRT字幕文件，自动调整时间轴偏移。

#### `gemini_role_analysis(audio_url: str, srt_content: str, output_file: str) -> bool`
使用Google Gemini AI分析音频中的说话人角色。

#### `generate_speaker_srt(gemini_json: str, output_file: str) -> bool`
根据AI分析结果生成带说话人标注的SRT文件。

#### `process_all(audio_url: str) -> Dict[str, str]`
执行完整的处理流程，返回所有输出文件路径。

## 输出文件

处理完成后会生成以下文件：

- `merged_all_chapters.wav` - 合并后的音频文件
- `merged_all_chapters.srt` - 合并后的字幕文件
- `gemini_role_analysis.json` - AI角色分析结果（JSON格式）
- `merged_all_chapters_with_speaker.srt` - 带说话人标注的字幕文件

## 配置说明

### Gemini AI配置

在使用前需要配置以下环境变量：

```bash
export GOOGLE_CLOUD_PROJECT="your-project-id"
export GOOGLE_CLOUD_REGION="global"
```

### 文件命名规范

- 章节文件夹: `chapter_1`, `chapter_2`, ...
- 音频文件: `ori_audio.wav`
- 字幕文件: `ori.srt`

## AI分析输出格式

Gemini AI分析结果为JSON格式，包含以下字段：

```json
[
  {
    "speaker": "张三",
    "start_time": "00:00:01,000",
    "end_time": "00:00:05,000",
    "gender": "男",
    "text": "你好，欢迎收听我们的节目。"
  },
  {
    "speaker": "李四",
    "start_time": "00:00:06,000",
    "end_time": "00:00:10,000",
    "gender": "女",
    "text": "谢谢，很高兴能参与这次讨论。"
  }
]
```

## 错误处理

程序包含完善的错误处理机制：

- **FFmpeg检查**: 自动检测FFmpeg是否正确安装
- **文件验证**: 检查音频和字幕文件是否存在
- **编码处理**: 自动处理UTF-8和GBK编码的字幕文件
- **JSON解析**: 智能处理Gemini返回的各种JSON格式

## 注意事项

1. **音频格式**: 建议使用WAV格式的音频文件以获得最佳兼容性
2. **字幕格式**: 仅支持标准SRT格式的字幕文件
3. **网络连接**: Gemini AI分析需要稳定的网络连接
4. **处理时间**: AI分析可能需要较长时间，请耐心等待
5. **配额限制**: 注意Google Cloud的API调用配额限制

## 故障排除

### 常见问题

**Q: FFmpeg未找到错误**
A: 请确保安装了完整的FFmpeg套件并添加到系统PATH中。

**Q: Gemini API调用失败**
A: 检查Google Cloud认证配置和网络连接。

**Q: 字幕时间轴不匹配**
A: 确保音频文件和字幕文件完全对应，检查文件完整性。

**Q: 编码错误**
A: 程序会自动尝试UTF-8和GBK编码，如仍有问题请检查字幕文件编码。

## 许可证

本项目采用MIT许可证。

## 更新日志

### v2.0.0 (简化版)
- 移除冗余的打印输出
- 重构为面向对象设计
- 改进错误处理机制
- 简化代码结构
- 增强类型注解

### v1.0.0 (原始版)
- 基础音频字幕合并功能
- Gemini AI角色识别
- 字幕比对和验证
