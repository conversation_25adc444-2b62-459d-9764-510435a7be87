#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
音频压缩工具测试脚本
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import numpy as np
import wave
import struct

from audio_compressor import AudioCompressor

def create_test_audio(filename: str, duration: int = 30, sample_rate: int = 44100) -> str:
    """
    创建测试音频文件
    
    Args:
        filename: 文件名
        duration: 时长（秒）
        sample_rate: 采样率
        
    Returns:
        创建的文件路径
    """
    print(f"创建测试音频文件: {filename} ({duration}秒)")
    
    # 生成正弦波测试音频
    t = np.linspace(0, duration, duration * sample_rate, False)
    # 混合多个频率的正弦波
    audio = (np.sin(2 * np.pi * 440 * t) * 0.3 +  # A4音符
             np.sin(2 * np.pi * 880 * t) * 0.2 +  # A5音符
             np.sin(2 * np.pi * 220 * t) * 0.2)   # A3音符
    
    # 转换为16位整数
    audio = (audio * 32767).astype(np.int16)
    
    # 写入WAV文件
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(2)  # 立体声
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        
        # 转换为立体声
        stereo_audio = np.column_stack((audio, audio))
        wav_file.writeframes(stereo_audio.tobytes())
    
    file_size = os.path.getsize(filename)
    print(f"测试文件创建成功: {file_size // 1024 // 1024}MB")
    
    return filename

def test_single_compression():
    """测试单文件压缩"""
    print("\n=== 测试单文件压缩 ===")
    
    # 创建测试文件
    test_file = "test_audio_large.wav"
    create_test_audio(test_file, duration=60)  # 60秒，约20MB
    
    try:
        # 创建压缩器
        compressor = AudioCompressor(target_size_mb=5.0)  # 压缩到5MB
        
        # 压缩文件
        output_file = "test_compressed.mp3"
        success = compressor.compress_audio(test_file, output_file, quality='balanced')
        
        if success:
            original_size = os.path.getsize(test_file)
            compressed_size = os.path.getsize(output_file)
            compression_ratio = (1 - compressed_size / original_size) * 100
            
            print(f"压缩成功!")
            print(f"原始大小: {original_size // 1024 // 1024}MB")
            print(f"压缩后大小: {compressed_size // 1024 // 1024}MB")
            print(f"压缩率: {compression_ratio:.1f}%")
            print(f"目标达成: {'是' if compressed_size <= 5 * 1024 * 1024 else '否'}")
            
            # 清理测试文件
            os.remove(output_file)
        else:
            print("压缩失败")
            
    finally:
        # 清理测试文件
        if os.path.exists(test_file):
            os.remove(test_file)

def test_different_sizes():
    """测试不同大小的文件压缩"""
    print("\n=== 测试不同大小文件压缩 ===")
    
    test_cases = [
        (10, "小文件"),   # 10秒，约3MB
        (60, "中等文件"), # 60秒，约20MB
        (180, "大文件")   # 180秒，约60MB
    ]
    
    compressor = AudioCompressor(target_size_mb=10.0)
    
    for duration, description in test_cases:
        print(f"\n测试 {description} ({duration}秒)")
        
        test_file = f"test_{duration}s.wav"
        output_file = f"compressed_{duration}s.mp3"
        
        try:
            create_test_audio(test_file, duration=duration)
            
            success = compressor.compress_audio(test_file, output_file)
            
            if success:
                original_size = os.path.getsize(test_file)
                compressed_size = os.path.getsize(output_file)
                
                print(f"  原始: {original_size // 1024 // 1024}MB")
                print(f"  压缩: {compressed_size // 1024 // 1024}MB")
                print(f"  达标: {'是' if compressed_size <= 10 * 1024 * 1024 else '否'}")
                
                # 清理输出文件
                os.remove(output_file)
            else:
                print(f"  压缩失败")
                
        finally:
            if os.path.exists(test_file):
                os.remove(test_file)

def test_quality_modes():
    """测试不同质量模式"""
    print("\n=== 测试不同质量模式 ===")
    
    test_file = "test_quality.wav"
    create_test_audio(test_file, duration=30)
    
    compressor = AudioCompressor(target_size_mb=3.0)
    qualities = ['high', 'balanced', 'aggressive']
    
    try:
        for quality in qualities:
            output_file = f"test_{quality}.mp3"
            
            print(f"\n测试 {quality} 质量模式")
            success = compressor.compress_audio(test_file, output_file, quality=quality)
            
            if success:
                size = os.path.getsize(output_file)
                print(f"  文件大小: {size // 1024}KB")
                print(f"  达标: {'是' if size <= 3 * 1024 * 1024 else '否'}")
                
                # 清理输出文件
                os.remove(output_file)
            else:
                print(f"  压缩失败")
                
    finally:
        if os.path.exists(test_file):
            os.remove(test_file)

def test_real_audio_file():
    """测试项目中的真实音频文件"""
    print("\n=== 测试真实音频文件 ===")
    
    # 查找项目中的音频文件
    audio_files = []
    for root, dirs, files in os.walk("归档"):
        for file in files:
            if file.endswith('.wav') and not file.startswith('test_'):
                file_path = os.path.join(root, file)
                if os.path.getsize(file_path) > 5 * 1024 * 1024:  # 大于5MB的文件
                    audio_files.append(file_path)
                    break  # 只测试一个文件
        if audio_files:
            break
    
    if not audio_files:
        print("未找到合适的测试文件")
        return
    
    test_file = audio_files[0]
    print(f"测试文件: {test_file}")
    
    original_size = os.path.getsize(test_file)
    print(f"原始大小: {original_size // 1024 // 1024}MB")
    
    compressor = AudioCompressor(target_size_mb=8.0)
    output_file = "real_audio_compressed.mp3"
    
    try:
        success = compressor.compress_audio(test_file, output_file)
        
        if success:
            compressed_size = os.path.getsize(output_file)
            compression_ratio = (1 - compressed_size / original_size) * 100
            
            print(f"压缩后大小: {compressed_size // 1024 // 1024}MB")
            print(f"压缩率: {compression_ratio:.1f}%")
            print(f"目标达成: {'是' if compressed_size <= 8 * 1024 * 1024 else '否'}")
            
            # 清理输出文件
            os.remove(output_file)
        else:
            print("压缩失败")
            
    except Exception as e:
        print(f"测试出错: {e}")
        if os.path.exists(output_file):
            os.remove(output_file)

def main():
    """主测试函数"""
    print("音频压缩工具测试开始")
    print("=" * 50)
    
    try:
        # 检查numpy是否可用，如果没有则跳过生成测试
        import numpy
        
        test_single_compression()
        test_different_sizes()
        test_quality_modes()
        
    except ImportError:
        print("numpy未安装，跳过生成测试音频的测试")
        print("可以运行: pip install numpy")
    
    # 测试真实文件（不需要numpy）
    test_real_audio_file()
    
    print("\n" + "=" * 50)
    print("所有测试完成！")

if __name__ == '__main__':
    main()
