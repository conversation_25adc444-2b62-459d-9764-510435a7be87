#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API 字幕说话人分析工具
使用 OpenAI SDK 访问 DeepSeek API 来分析字幕中的说话人身份
"""

import os
from openai import OpenAI

def read_subtitle_file(file_path):
    """读取字幕文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"读取文件失败: {e}")
        return None

def analyze_speakers_with_deepseek(subtitle_content):
    """使用DeepSeek API分析说话人身份"""
    
    # DeepSeek API 配置
    api_key = "***********************************"
    
    # 创建 OpenAI 客户端，配置为使用 DeepSeek API
    client = OpenAI(
        api_key=api_key, 
        base_url="https://api.deepseek.com"
    )
    
    # 构建分析提示词
    system_prompt = """你是一个专业的影视剧本分析师。请分析以下字幕内容，根据对话内容、语气、称谓等信息，推断每个speaker可能的人名或角色身份。

请注意：
1. 仔细分析对话中的称谓关系（如"妈妈"、"婆婆"、"儿媳"等）
2. 根据语气和说话内容判断角色性别和年龄
3. 分析角色之间的关系和身份
4. 如果能推断出具体人名，请给出；如果不能，请给出角色描述

请以JSON格式返回分析结果，格式如下：
{
    "speaker_1": {"name": "推断的人名或角色", "description": "详细描述", "confidence": "置信度(高/中/低)"},
    "speaker_2": {"name": "推断的人名或角色", "description": "详细描述", "confidence": "置信度(高/中/低)"},
    ...
}"""
    
    user_prompt = f"""请分析以下字幕内容中的说话人身份：

{subtitle_content}

请根据对话内容推断每个speaker的可能身份或人名。"""
    
    try:
        print("正在调用 DeepSeek API 分析说话人...")
        
        # 调用对话 API
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt},
            ],
            stream=False,
            temperature=0.3  # 降低随机性，提高分析准确性
        )
        
        # 输出响应内容
        print("API 调用成功！")
        print("\n=== DeepSeek AI 分析结果 ===")
        analysis_result = response.choices[0].message.content
        print(analysis_result)
        
        return analysis_result
        
    except Exception as e:
        print(f"API 调用失败: {e}")
        print("请检查:")
        print("1. API Key 是否正确")
        print("2. 网络连接是否正常")
        print("3. API 配额是否充足")
        return None

def analyze_subtitle_speakers(subtitle_file_path):
    """分析字幕文件中的说话人"""
    print(f"开始分析字幕文件: {subtitle_file_path}")
    
    # 检查文件是否存在
    if not os.path.exists(subtitle_file_path):
        print(f"错误: 文件不存在 - {subtitle_file_path}")
        return
    
    # 读取字幕内容
    subtitle_content = read_subtitle_file(subtitle_file_path)
    if not subtitle_content:
        return
    
    print("\n=== 字幕内容预览 ===")
    lines = subtitle_content.strip().split('\n')
    for i, line in enumerate(lines[:5]):  # 显示前5行
        print(f"{i+1}: {line}")
    if len(lines) > 5:
        print(f"... (共{len(lines)}行)")
    
    # 使用DeepSeek分析
    analysis_result = analyze_speakers_with_deepseek(subtitle_content)
    
    if analysis_result:
        # 保存分析结果
        output_file = subtitle_file_path.replace('.txt', '_speaker_analysis.txt')
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== DeepSeek AI 说话人分析结果 ===\n\n")
                f.write(analysis_result)
                f.write(f"\n\n=== 原始字幕内容 ===\n\n")
                f.write(subtitle_content)
            print(f"\n分析结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存结果失败: {e}")

def test_deepseek_api():
    """测试 DeepSeek API 连接"""
    
    api_key = "***********************************"
    
    client = OpenAI(
        api_key=api_key, 
        base_url="https://api.deepseek.com"
    )
    
    try:
        print("正在测试 DeepSeek API 连接...")
        
        response = client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": "You are a helpful assistant"},
                {"role": "user", "content": "Hello"},
            ],
            stream=False
        )
        
        print("API 连接测试成功！")
        print("响应内容:", response.choices[0].message.content)
        return True
        
    except Exception as e:
        print(f"API 连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("DeepSeek API 字幕说话人分析工具")
    print("=" * 50)
    
    # 首先测试API连接
    if test_deepseek_api():
        print("\n" + "=" * 50)
        
        # 分析字幕文件
        subtitle_file = "归档/chapter_002/matched_subtitles.txt"
        analyze_subtitle_speakers(subtitle_file)
    else:
        print("API连接失败，请检查配置后重试")
