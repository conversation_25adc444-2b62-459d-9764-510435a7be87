#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
归档音频和字幕合并工具
按顺序合并归档目录下所有chapter的ori_audio.wav文件，并按时长拼接对应的ori.srt字幕文件
"""

import os
import re
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import timedelta

def get_audio_duration(audio_file):
    """获取音频文件时长（秒）"""
    try:
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            str(audio_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, 
                               encoding='utf-8', errors='ignore')
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            return float(data['format']['duration'])
        else:
            print(f"   ⚠️  无法获取时长: {audio_file}")
            return 0.0
            
    except Exception as e:
        print(f"   ❌ 获取时长失败: {e}")
        return 0.0

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数 (HH:MM:SS,mmm)"""
    time_str = time_str.replace(',', '.')
    parts = time_str.split(':')
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = float(parts[2])
    return hours * 3600 + minutes * 60 + seconds

def format_srt_time(seconds: float) -> str:
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')

def parse_srt_file(file_path: str) -> List[Dict]:
    """解析SRT文件"""
    subtitles = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read().strip()
        except Exception as e:
            print(f"   ❌ 无法读取文件 {file_path}: {e}")
            return []
    
    # 分割字幕块
    blocks = content.split('\n\n')
    
    for block in blocks:
        if not block.strip():
            continue
            
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue
            
        # 解析序号
        try:
            index = int(lines[0])
        except:
            continue
            
        # 解析时间轴
        time_line = lines[1]
        time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
        if not time_match:
            continue
            
        start_time = time_match.group(1)
        end_time = time_match.group(2)
        
        # 解析字幕文本
        text = '\n'.join(lines[2:])
        
        subtitles.append({
            'index': index,
            'start_time': start_time,
            'end_time': end_time,
            'start_seconds': parse_srt_time(start_time),
            'end_seconds': parse_srt_time(end_time),
            'text': text
        })
    
    return subtitles

def concatenate_audio_files(audio_files: List[Path], output_file: str) -> bool:
    """使用FFmpeg拼接音频文件"""
    try:
        # 创建临时文件列表
        filelist_path = "temp_audio_filelist.txt"
        
        with open(filelist_path, 'w', encoding='utf-8') as f:
            for audio_file in audio_files:
                # FFmpeg要求路径用单引号包围，并转义特殊字符
                escaped_path = str(audio_file).replace("'", "'\"'\"'")
                f.write(f"file '{escaped_path}'\n")
        
        # FFmpeg拼接命令
        cmd = [
            'ffmpeg',
            '-f', 'concat',
            '-safe', '0',
            '-i', filelist_path,
            '-c', 'copy',
            '-y',
            str(output_file)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True,
                               encoding='utf-8', errors='ignore')
        
        # 清理临时文件
        if os.path.exists(filelist_path):
            os.remove(filelist_path)
        
        if result.returncode == 0:
            return True
        else:
            print(f"❌ FFmpeg拼接失败:")
            print(f"   {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 拼接失败: {e}")
        return False

def merge_subtitles(subtitle_files: List[Tuple[str, float]], output_file: str) -> bool:
    """合并字幕文件，按音频时长调整时间轴"""
    try:
        all_subtitles = []
        current_offset = 0.0
        subtitle_index = 1
        
        for srt_file, duration in subtitle_files:
            print(f"   📄 处理字幕: {Path(srt_file).name}")
            
            subtitles = parse_srt_file(srt_file)
            
            for sub in subtitles:
                # 调整时间轴
                new_start = sub['start_seconds'] + current_offset
                new_end = sub['end_seconds'] + current_offset
                
                all_subtitles.append({
                    'index': subtitle_index,
                    'start_time': format_srt_time(new_start),
                    'end_time': format_srt_time(new_end),
                    'text': sub['text']
                })
                subtitle_index += 1
            
            # 累加偏移量
            current_offset += duration
            print(f"      时长: {duration:.2f}s, 累计偏移: {current_offset:.2f}s")
        
        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for sub in all_subtitles:
                f.write(f"{sub['index']}\n")
                f.write(f"{sub['start_time']} --> {sub['end_time']}\n")
                f.write(f"{sub['text']}\n\n")
        
        print(f"   ✅ 字幕合并完成，共 {len(all_subtitles)} 条字幕")
        return True
        
    except Exception as e:
        print(f"❌ 字幕合并失败: {e}")
        return False

def get_chapter_folders(archive_dir: str) -> List[str]:
    """获取章节文件夹列表，按数字顺序排序"""
    archive_path = Path(archive_dir)
    if not archive_path.exists():
        print(f"❌ 归档目录不存在: {archive_dir}")
        return []
    
    # 查找所有chapter_xxx目录
    chapter_folders = []
    for item in archive_path.iterdir():
        if item.is_dir() and item.name.startswith('chapter_'):
            chapter_folders.append(item.name)
    
    # 按章节号排序
    def extract_chapter_number(folder_name):
        match = re.search(r'chapter_(\d+)', folder_name)
        return int(match.group(1)) if match else 0
    
    chapter_folders.sort(key=extract_chapter_number)
    return chapter_folders

def main():
    print("🎵 归档音频和字幕合并工具")
    print("=" * 60)
    
    # 配置
    archive_dir = "归档"
    output_audio = "merged_all_chapters.wav"
    output_subtitle = "merged_all_chapters.srt"
    timeline_file = "merge_timeline.json"
    
    # 检查FFmpeg
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
        print("✅ FFmpeg检测成功")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 请确保安装了完整的FFmpeg套件")
        return 1
    
    # 获取章节目录
    chapter_folders = get_chapter_folders(archive_dir)
    if not chapter_folders:
        print(f"❌ 在 {archive_dir} 目录下未找到章节文件夹")
        return 1
    
    print(f"📁 找到 {len(chapter_folders)} 个章节:")
    for folder in chapter_folders:
        print(f"   • {folder}")
    print()
    
    # 收集音频和字幕文件
    audio_files = []
    subtitle_files = []
    timeline_info = []
    current_time = 0.0
    
    print("🔍 扫描文件...")
    for folder in chapter_folders:
        chapter_path = Path(archive_dir) / folder
        
        # 查找ori_audio.wav
        audio_file = chapter_path / "ori_audio.wav"
        srt_file = chapter_path / "ori.srt"
        
        if not audio_file.exists():
            print(f"   ⚠️  {folder}: 未找到 ori_audio.wav")
            continue
            
        if not srt_file.exists():
            print(f"   ⚠️  {folder}: 未找到 ori.srt")
            continue
        
        # 获取音频时长
        duration = get_audio_duration(audio_file)
        if duration <= 0:
            print(f"   ⚠️  {folder}: 音频时长无效")
            continue
        
        audio_files.append(audio_file)
        subtitle_files.append((str(srt_file), duration))
        
        # 记录时间轴信息
        timeline_info.append({
            "chapter": folder,
            "audio_file": str(audio_file),
            "srt_file": str(srt_file),
            "start_time": current_time,
            "end_time": current_time + duration,
            "duration": duration
        })
        
        print(f"   ✅ {folder}: {duration:.2f}s")
        current_time += duration
    
    if not audio_files:
        print("❌ 未找到有效的音频文件")
        return 1
    
    print(f"\n📊 总览:")
    print(f"   • 有效章节: {len(audio_files)}")
    print(f"   • 总时长: {current_time:.2f}s ({current_time/60:.1f}分钟)")
    print()
    
    # 合并音频
    print("🎵 开始合并音频...")
    success = concatenate_audio_files(audio_files, output_audio)
    
    if not success:
        print("❌ 音频合并失败")
        return 1
    
    print(f"   ✅ 音频合并完成: {output_audio}")
    
    # 合并字幕
    print("\n📄 开始合并字幕...")
    success = merge_subtitles(subtitle_files, output_subtitle)
    
    if not success:
        print("❌ 字幕合并失败")
        return 1
    
    print(f"   ✅ 字幕合并完成: {output_subtitle}")
    
    # 保存时间轴信息
    print(f"\n⏱️  保存时间轴信息: {timeline_file}")
    with open(timeline_file, 'w', encoding='utf-8') as f:
        json.dump({
            "total_duration": current_time,
            "total_chapters": len(audio_files),
            "output_audio": output_audio,
            "output_subtitle": output_subtitle,
            "timeline": timeline_info
        }, f, ensure_ascii=False, indent=2)
    
    print("\n🎉 合并完成!")
    print(f"   🎵 输出音频: {output_audio}")
    print(f"   📄 输出字幕: {output_subtitle}")
    print(f"   ⏱️  时间轴: {timeline_file}")
    print(f"   ⏱️  总时长: {current_time:.2f}s ({current_time/60:.1f}分钟)")

if __name__ == "__main__":
    main()
