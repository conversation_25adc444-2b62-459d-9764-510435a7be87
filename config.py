#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
讯飞语音识别API配置示例
请复制此文件为config.py并填入您的真实API信息
"""

# 讯飞开放平台应用信息
# 请在 https://console.xfyun.cn/ 创建应用并获取以下信息
XUNFEI_CONFIG = {
    "APP_ID": "1097a470",          # 应用ID，32位字符串
    "API_KEY": "d40aee33d76cc16c6966f44951fd81b3",        # API Key，32位字符串  
    "API_SECRET": "MzE0MzBmZTE0ODAxNWZhNzMyNjQ4NGFi"   # API Secret，32位字符串
}

# 音频识别参数配置
AUDIO_CONFIG = {
    "domain": "slm",           # 指定访问的领域，大模型中文语音识别固定为slm
    "language": "zh_cn",       # 语种，中文为zh_cn，英文为en_us
    "accent": "mandarin",      # 方言，普通话为mandarin
    "encoding": "raw",         # 音频编码，raw代表PCM格式，lame代表MP3格式
    "sample_rate": 16000,      # 采样率，支持16000或8000
    "channels": 1,             # 声道数，固定为1
    "bit_depth": 16,           # 位深，固定为16
    "eos": 6000,              # 静音多少毫秒停止识别
    "vinfo": 1,               # 句子级别帧对齐
    "dwa": "wpgs"             # 流式识别PGS，返回速度更快，仅中文支持
}

# 传输参数配置
TRANSPORT_CONFIG = {
    "frame_size": 1280,        # 每帧音频大小（字节）
    "interval": 0.04,          # 发送间隔（秒），建议40ms
    "use_ssl": True,           # 是否使用SSL连接（推荐）
    "timeout": 10.0            # 超时时间（秒）
}
