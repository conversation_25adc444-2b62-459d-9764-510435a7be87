import os
import json
from google import genai
from google.genai.types import (
    FunctionDeclaration,
    GenerateContentConfig,
    GoogleSearch,
    HarmBlockThreshold,
    HarmCategory,
    MediaResolution,
    Part,
    Retrieval,
    SafetySetting,
    Tool,
    ToolCodeExecution,
    VertexAISearch,
)

PROJECT_ID = "gen-lang-client-0255558294"

LOCATION = os.environ.get("GOOGLE_CLOUD_REGION", "global")

client = genai.Client(vertexai=True, project=PROJECT_ID, location=LOCATION)

MODEL_ID = "gemini-2.5-pro"

# 读取SRT文件内容
def read_srt_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print(f"文件 {file_path} 未找到")
        return ""
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return ""

# 读取SRT文件
srt_content = read_srt_file("split_srt/chapter_011_with_speaker.srt")

# 构建包含SRT内容的prompt
prompt = f"""根据这段音频，参考提供的srt字幕，并按照srt字幕的格式,识别音频中的说话人，台词。返回字段包含spearker, start_time, end_time, text。返回json格式。

*要求：
1. 不要修改参考SRT文件内容的时间轴,不要在时间轴上添加speaker。
2. 可以修改参考SRT文件识别错的台词，不要添加语气词、表情内容，只需要人物说话的台词。
3. 可以修改参考SRT文件识别错的speaker，要求根据音频进行解析，注意男女声的区别，要求speaker能做到上下文合理。

*参考的SRT文件内容：
{srt_content}

请根据音频内容生成准确的字幕信息。"""


response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(
            file_uri="https://gidfyoqecthbormpbirt.supabase.co/storage/v1/object/public/audios/audio_011.wav",
            mime_type="audio/mpeg",
        ),
        prompt,
    ],
    config=GenerateContentConfig(audio_timestamp=True, temperature=1.0),
)

# 保存JSON输出到文件
try:
    # 解析JSON响应
    json_data = json.loads(response.text)
    
    # 保存到JSON文件
    output_file = "subtitle_output_011.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    
    print(f"JSON输出已保存到 {output_file}")
    print("输出内容:")
    print(json.dumps(json_data, ensure_ascii=False, indent=2))
    
except json.JSONDecodeError as e:
    print(f"JSON解析错误: {e}")
    print("原始响应:")
    print(response.text)
    
    # 如果JSON解析失败，保存原始文本
    with open("subtitle_output_raw_011.txt", 'w', encoding='utf-8') as f:
        f.write(response.text)
    print("原始响应已保存到 subtitle_output_raw_011.txt")
    
except Exception as e:
    print(f"保存文件时出错: {e}")
    print("原始响应:")
    print(response.text)