#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕时间轴对比工具
对比episodes_subtitles里的章节SRT文件和原始归档的SRT文件
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple
import json

def parse_srt_file(file_path: str) -> List[Dict]:
    """解析SRT文件"""
    subtitles = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read().strip()
        except Exception as e:
            print(f"   ❌ 无法读取文件 {file_path}: {e}")
            return []
    
    # 分割字幕块
    blocks = content.split('\n\n')
    
    for block in blocks:
        if not block.strip():
            continue
            
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue
            
        # 解析序号
        try:
            index = int(lines[0])
        except:
            continue
            
        # 解析时间轴
        time_line = lines[1]
        time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
        if not time_match:
            continue
            
        start_time = time_match.group(1)
        end_time = time_match.group(2)
        
        # 解析字幕文本
        text = '\n'.join(lines[2:])
        
        subtitles.append({
            'index': index,
            'start_time': start_time,
            'end_time': end_time,
            'text': text
        })
    
    return subtitles

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    parts = time_str.split(':')
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = float(parts[2])
    return hours * 3600 + minutes * 60 + seconds

def compare_subtitle_files(episodes_file: str, original_file: str, chapter_name: str) -> Dict:
    """对比两个字幕文件"""
    
    print(f"\n📊 对比 {chapter_name}:")
    print(f"   分集文件: {episodes_file}")
    print(f"   原始文件: {original_file}")
    
    # 解析两个文件
    episodes_subs = parse_srt_file(episodes_file)
    original_subs = parse_srt_file(original_file)
    
    print(f"   分集字幕数: {len(episodes_subs)}")
    print(f"   原始字幕数: {len(original_subs)}")
    
    comparison_result = {
        'chapter': chapter_name,
        'episodes_file': episodes_file,
        'original_file': original_file,
        'episodes_count': len(episodes_subs),
        'original_count': len(original_subs),
        'timeline_matches': [],
        'timeline_differences': [],
        'text_differences': [],
        'missing_in_episodes': [],
        'missing_in_original': [],
        'summary': {}
    }
    
    # 创建时间轴字典用于快速查找
    episodes_by_time = {(sub['start_time'], sub['end_time']): sub for sub in episodes_subs}
    original_by_time = {(sub['start_time'], sub['end_time']): sub for sub in original_subs}
    
    # 找到匹配的时间轴
    matched_timelines = 0
    timeline_mismatches = 0
    text_differences = 0
    
    for original_sub in original_subs:
        time_key = (original_sub['start_time'], original_sub['end_time'])
        
        if time_key in episodes_by_time:
            # 时间轴匹配
            episodes_sub = episodes_by_time[time_key]
            matched_timelines += 1
            
            comparison_result['timeline_matches'].append({
                'timeline': f"{original_sub['start_time']} --> {original_sub['end_time']}",
                'original_index': original_sub['index'],
                'episodes_index': episodes_sub['index'],
                'original_text': original_sub['text'],
                'episodes_text': episodes_sub['text']
            })
            
            # 检查文本是否一致
            if original_sub['text'].strip() != episodes_sub['text'].strip():
                text_differences += 1
                comparison_result['text_differences'].append({
                    'timeline': f"{original_sub['start_time']} --> {original_sub['end_time']}",
                    'original_text': original_sub['text'],
                    'episodes_text': episodes_sub['text']
                })
        else:
            # 在分集文件中找不到对应时间轴
            timeline_mismatches += 1
            comparison_result['missing_in_episodes'].append({
                'timeline': f"{original_sub['start_time']} --> {original_sub['end_time']}",
                'index': original_sub['index'],
                'text': original_sub['text']
            })
    
    # 找到分集文件中多出的时间轴
    for episodes_sub in episodes_subs:
        time_key = (episodes_sub['start_time'], episodes_sub['end_time'])
        if time_key not in original_by_time:
            comparison_result['missing_in_original'].append({
                'timeline': f"{episodes_sub['start_time']} --> {episodes_sub['end_time']}",
                'index': episodes_sub['index'],
                'text': episodes_sub['text']
            })
    
    # 统计结果
    comparison_result['summary'] = {
        'timeline_matches': matched_timelines,
        'timeline_mismatches': timeline_mismatches,
        'text_differences': text_differences,
        'missing_in_episodes': len(comparison_result['missing_in_episodes']),
        'missing_in_original': len(comparison_result['missing_in_original']),
        'timeline_match_rate': matched_timelines / max(len(original_subs), 1) * 100,
        'text_match_rate': (matched_timelines - text_differences) / max(matched_timelines, 1) * 100
    }
    
    # 打印简要结果
    summary = comparison_result['summary']
    print(f"   ✅ 时间轴匹配: {summary['timeline_matches']}")
    print(f"   ❌ 时间轴差异: {summary['timeline_mismatches']}")
    print(f"   📝 文本差异: {summary['text_differences']}")
    print(f"   ⚠️  分集缺失: {summary['missing_in_episodes']}")
    print(f"   ⚠️  原始缺失: {summary['missing_in_original']}")
    print(f"   📈 时间轴匹配率: {summary['timeline_match_rate']:.1f}%")
    print(f"   📈 文本匹配率: {summary['text_match_rate']:.1f}%")
    
    return comparison_result

def get_chapter_list() -> List[str]:
    """获取章节列表"""
    episodes_dir = Path("episodes_subtitles")
    if not episodes_dir.exists():
        return []
    
    chapters = []
    for file in episodes_dir.glob("chapter_*.srt"):
        if not file.name.endswith("_with_speaker.srt"):
            chapter_name = file.stem
            chapters.append(chapter_name)
    
    return sorted(chapters)

def main():
    print("🔍 字幕时间轴对比工具")
    print("=" * 50)
    
    # 获取章节列表
    chapters = get_chapter_list()
    if not chapters:
        print("❌ 未找到分集字幕文件")
        return 1
    
    print(f"📁 找到 {len(chapters)} 个章节")
    
    all_results = []
    total_timeline_matches = 0
    total_timeline_differences = 0
    total_text_differences = 0
    
    # 逐个对比章节
    for chapter in chapters:
        episodes_file = f"episodes_subtitles/{chapter}.srt"
        original_file = f"归档/{chapter}/ori.srt"
        
        # 检查文件存在性
        if not os.path.exists(episodes_file):
            print(f"   ⚠️  分集文件不存在: {episodes_file}")
            continue
            
        if not os.path.exists(original_file):
            print(f"   ⚠️  原始文件不存在: {original_file}")
            continue
        
        # 对比文件
        result = compare_subtitle_files(episodes_file, original_file, chapter)
        all_results.append(result)
        
        # 累计统计
        summary = result['summary']
        total_timeline_matches += summary['timeline_matches']
        total_timeline_differences += summary['timeline_mismatches']
        total_text_differences += summary['text_differences']
    
    # 生成总结报告
    print(f"\n📊 总体对比结果:")
    print(f"   章节总数: {len(all_results)}")
    print(f"   时间轴匹配总数: {total_timeline_matches}")
    print(f"   时间轴差异总数: {total_timeline_differences}")
    print(f"   文本差异总数: {total_text_differences}")
    
    if total_timeline_matches + total_timeline_differences > 0:
        overall_match_rate = total_timeline_matches / (total_timeline_matches + total_timeline_differences) * 100
        print(f"   总体时间轴匹配率: {overall_match_rate:.1f}%")
    
    # 保存详细报告
    report_file = "subtitle_comparison_report.json"
    report = {
        'comparison_date': str(Path().absolute()),
        'total_chapters': len(all_results),
        'overall_statistics': {
            'total_timeline_matches': total_timeline_matches,
            'total_timeline_differences': total_timeline_differences,
            'total_text_differences': total_text_differences,
            'overall_match_rate': overall_match_rate if 'overall_match_rate' in locals() else 0
        },
        'chapter_results': all_results
    }
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    # 显示有问题的章节
    problem_chapters = []
    for result in all_results:
        summary = result['summary']
        if (summary['timeline_mismatches'] > 0 or 
            summary['text_differences'] > 0 or 
            summary['missing_in_episodes'] > 0 or 
            summary['missing_in_original'] > 0):
            problem_chapters.append(result['chapter'])
    
    if problem_chapters:
        print(f"\n⚠️  需要注意的章节:")
        for chapter in problem_chapters:
            print(f"   • {chapter}")
    else:
        print(f"\n✅ 所有章节的时间轴都完全一致!")

if __name__ == "__main__":
    main()
