#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复speaker_output.txt的时间轴，使其与实际的音频拼接时间轴对应
"""

import json
import re

def parse_srt_time(time_str: str) -> float:
    """将SRT时间格式转换为秒数"""
    time_str = time_str.replace(',', '.')
    parts = time_str.split(':')
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = float(parts[2])
    return hours * 3600 + minutes * 60 + seconds

def format_srt_time(seconds: float) -> str:
    """将秒数转换为SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')

def fix_speaker_timeline():
    """修复speaker_output.txt的时间轴"""
    
    # 读取原始文件
    with open('speaker_output.txt', 'r', encoding='utf-8') as f:
        content = f.read().strip()
        if content.startswith('```json'):
            content = content[7:]
        if content.endswith('```'):
            content = content[:-3]
        
        data = json.loads(content)
    
    print(f"原始数据: {len(data)} 条字幕")
    
    # 获取时间范围
    times = []
    for item in data:
        start_time = parse_srt_time(item['start'])
        end_time = parse_srt_time(item['end'])
        times.extend([start_time, end_time])
    
    min_time = min(times)
    max_time = max(times)
    original_duration = max_time - min_time
    
    print(f"原始时间范围: {min_time:.2f}s - {max_time:.2f}s")
    print(f"原始总时长: {original_duration:.2f}s ({original_duration/60:.2f}分钟)")
    
    # 目标总时长（基于音频文件）
    target_duration = 854.87  # 秒
    
    # 计算缩放比例
    scale_factor = target_duration / original_duration
    print(f"时间缩放比例: {scale_factor:.4f}")
    
    # 调整所有时间戳
    for item in data:
        original_start = parse_srt_time(item['start'])
        original_end = parse_srt_time(item['end'])
        
        # 减去最小时间，然后按比例缩放
        adjusted_start = (original_start - min_time) * scale_factor
        adjusted_end = (original_end - min_time) * scale_factor
        
        item['start'] = format_srt_time(adjusted_start)
        item['end'] = format_srt_time(adjusted_end)
    
    # 保存修复后的文件
    with open('speaker_output_fixed.txt', 'w', encoding='utf-8') as f:
        f.write('```json\n')
        json.dump(data, f, ensure_ascii=False, indent=2)
        f.write('\n```')
    
    print(f"已保存修复后的文件: speaker_output_fixed.txt")
    
    # 验证修复结果
    new_times = []
    for item in data:
        start_time = parse_srt_time(item['start'])
        end_time = parse_srt_time(item['end'])
        new_times.extend([start_time, end_time])
    
    new_min_time = min(new_times)
    new_max_time = max(new_times)
    new_duration = new_max_time - new_min_time
    
    print(f"修复后时间范围: {new_min_time:.2f}s - {new_max_time:.2f}s")
    print(f"修复后总时长: {new_duration:.2f}s ({new_duration/60:.2f}分钟)")

if __name__ == '__main__':
    fix_speaker_timeline()
