#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
音频压缩工具
将任意大小的音频文件压缩到指定大小以下（默认10MB）
支持多种音频格式，智能调整参数以达到最佳压缩效果
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import Optional, Tuple, Union
import json
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AudioCompressor:
    """音频压缩器"""
    
    def __init__(self, target_size_mb: float = 10.0):
        """
        初始化音频压缩器
        
        Args:
            target_size_mb: 目标文件大小（MB）
        """
        self.target_size_mb = target_size_mb
        self.target_size_bytes = int(target_size_mb * 1024 * 1024)
        
        # 检查ffmpeg是否可用
        self._check_ffmpeg()
        
    def _check_ffmpeg(self):
        """检查ffmpeg是否安装"""
        try:
            subprocess.run(['ffmpeg', '-version'], 
                         capture_output=True, check=True)
            logger.info("FFmpeg检测成功")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("FFmpeg未找到，请先安装FFmpeg")
            logger.info("Windows安装方法: https://ffmpeg.org/download.html")
            logger.info("或使用chocolatey: choco install ffmpeg")
            sys.exit(1)
    
    def get_audio_info(self, input_path: str) -> dict:
        """
        获取音频文件信息
        
        Args:
            input_path: 输入音频文件路径
            
        Returns:
            音频信息字典
        """
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', input_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            info = json.loads(result.stdout)
            
            # 提取音频流信息
            audio_stream = None
            for stream in info['streams']:
                if stream['codec_type'] == 'audio':
                    audio_stream = stream
                    break
            
            if not audio_stream:
                raise ValueError("未找到音频流")
            
            return {
                'duration': float(info['format'].get('duration', 0)),
                'size': int(info['format'].get('size', 0)),
                'bitrate': int(info['format'].get('bit_rate', 0)),
                'sample_rate': int(audio_stream.get('sample_rate', 44100)),
                'channels': int(audio_stream.get('channels', 2)),
                'codec': audio_stream.get('codec_name', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"获取音频信息失败: {e}")
            raise
    
    def calculate_target_bitrate(self, duration: float, target_size: int) -> int:
        """
        计算目标比特率
        
        Args:
            duration: 音频时长（秒）
            target_size: 目标文件大小（字节）
            
        Returns:
            目标比特率（bps）
        """
        if duration <= 0:
            return 128000  # 默认128kbps
        
        # 预留一些空间给容器开销（约5%）
        target_size = int(target_size * 0.95)
        
        # 计算比特率 (bps)
        target_bitrate = int((target_size * 8) / duration)
        
        # 限制比特率范围
        min_bitrate = 32000   # 32kbps
        max_bitrate = 320000  # 320kbps
        
        target_bitrate = max(min_bitrate, min(target_bitrate, max_bitrate))
        
        logger.info(f"计算目标比特率: {target_bitrate}bps ({target_bitrate//1000}kbps)")
        return target_bitrate
    
    def compress_audio(self, input_path: str, output_path: str, 
                      quality: str = 'balanced') -> bool:
        """
        压缩音频文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            quality: 压缩质量 ('high', 'balanced', 'aggressive')
            
        Returns:
            是否成功
        """
        try:
            logger.info(f"开始压缩: {input_path}")
            
            # 获取原始音频信息
            info = self.get_audio_info(input_path)
            logger.info(f"原始文件信息: 大小={info['size']//1024//1024}MB, "
                       f"时长={info['duration']:.1f}s, 比特率={info['bitrate']//1000}kbps")
            
            if info['size'] <= self.target_size_bytes:
                logger.info("文件已经小于目标大小，直接复制")
                shutil.copy2(input_path, output_path)
                return True
            
            # 计算目标比特率
            target_bitrate = self.calculate_target_bitrate(info['duration'], self.target_size_bytes)
            
            # 根据质量设置参数
            params = self._get_compression_params(quality, target_bitrate, info)
            
            # 执行压缩
            success = self._compress_with_params(input_path, output_path, params)
            
            if success:
                # 检查输出文件大小
                output_size = os.path.getsize(output_path)
                logger.info(f"压缩完成: {output_size//1024//1024}MB")
                
                if output_size > self.target_size_bytes:
                    logger.warning("文件仍然过大，尝试更激进的压缩")
                    return self._aggressive_compress(input_path, output_path, info)
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"压缩失败: {e}")
            return False
    
    def _get_compression_params(self, quality: str, target_bitrate: int, info: dict) -> dict:
        """获取压缩参数"""
        params = {
            'codec': 'libmp3lame',
            'bitrate': target_bitrate,
            'sample_rate': info['sample_rate'],
            'channels': info['channels']
        }
        
        if quality == 'high':
            # 高质量：保持原始采样率和声道
            params['quality'] = 2
            
        elif quality == 'balanced':
            # 平衡模式：适当降低参数
            params['sample_rate'] = min(44100, info['sample_rate'])
            params['channels'] = min(2, info['channels'])
            params['quality'] = 4
            
        elif quality == 'aggressive':
            # 激进模式：大幅降低参数
            params['sample_rate'] = 22050 if info['sample_rate'] > 22050 else info['sample_rate']
            params['channels'] = 1  # 单声道
            params['quality'] = 6
            params['bitrate'] = min(target_bitrate, 96000)  # 最大96kbps
        
        return params
    
    def _compress_with_params(self, input_path: str, output_path: str, params: dict) -> bool:
        """使用指定参数压缩"""
        try:
            cmd = [
                'ffmpeg', '-i', input_path,
                '-c:a', params['codec'],
                '-b:a', str(params['bitrate']),
                '-ar', str(params['sample_rate']),
                '-ac', str(params['channels']),
                '-q:a', str(params.get('quality', 4)),
                '-y',  # 覆盖输出文件
                output_path
            ]
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"FFmpeg错误: {result.stderr}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"压缩执行失败: {e}")
            return False
    
    def _aggressive_compress(self, input_path: str, output_path: str, info: dict) -> bool:
        """激进压缩模式"""
        logger.info("启动激进压缩模式")
        
        # 使用更低的比特率
        target_bitrate = self.calculate_target_bitrate(info['duration'], self.target_size_bytes)
        target_bitrate = int(target_bitrate * 0.8)  # 降低20%
        
        params = {
            'codec': 'libmp3lame',
            'bitrate': max(32000, target_bitrate),  # 最低32kbps
            'sample_rate': 22050,
            'channels': 1,
            'quality': 9  # 最低质量但最小文件
        }
        
        return self._compress_with_params(input_path, output_path, params)
    
    def compress_multiple(self, input_dir: str, output_dir: str, 
                         pattern: str = "*.wav") -> list:
        """
        批量压缩音频文件
        
        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            pattern: 文件匹配模式
            
        Returns:
            处理结果列表
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 查找匹配的文件
        files = list(input_path.glob(pattern))
        
        results = []
        
        for file_path in files:
            logger.info(f"处理文件: {file_path.name}")
            
            # 生成输出文件名（改为mp3）
            output_file = output_path / f"{file_path.stem}_compressed.mp3"
            
            # 压缩文件
            success = self.compress_audio(str(file_path), str(output_file))
            
            results.append({
                'input': str(file_path),
                'output': str(output_file),
                'success': success
            })
        
        return results

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='音频压缩工具')
    parser.add_argument('input', help='输入音频文件或目录')
    parser.add_argument('-o', '--output', help='输出文件或目录')
    parser.add_argument('-s', '--size', type=float, default=10.0, 
                       help='目标大小(MB)，默认10MB')
    parser.add_argument('-q', '--quality', choices=['high', 'balanced', 'aggressive'],
                       default='balanced', help='压缩质量')
    parser.add_argument('--batch', action='store_true', help='批量处理目录')
    
    args = parser.parse_args()
    
    # 创建压缩器
    compressor = AudioCompressor(args.size)
    
    if args.batch:
        # 批量处理
        if not args.output:
            args.output = 'compressed_output'
        
        results = compressor.compress_multiple(args.input, args.output)
        
        # 输出结果
        success_count = sum(1 for r in results if r['success'])
        print(f"\n批量处理完成: {success_count}/{len(results)} 个文件成功")
        
    else:
        # 单文件处理
        if not args.output:
            input_path = Path(args.input)
            args.output = f"{input_path.stem}_compressed.mp3"
        
        success = compressor.compress_audio(args.input, args.output, args.quality)
        
        if success:
            print(f"压缩成功: {args.output}")
        else:
            print("压缩失败")
            sys.exit(1)

if __name__ == '__main__':
    main()
