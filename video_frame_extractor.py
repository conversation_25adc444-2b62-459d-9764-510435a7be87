#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频帧提取器 - 基于字幕时间轴按说话人提取视频帧
"""

import cv2
import json
import os
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Tuple


class VideoFrameExtractor:
    def __init__(self, video_path: str, subtitle_data: List[Dict], output_dir: str = "extracted_frames"):
        """
        初始化视频帧提取器
        
        Args:
            video_path: 视频文件路径
            subtitle_data: 字幕数据列表
            output_dir: 输出目录
        """
        self.video_path = video_path
        self.subtitle_data = subtitle_data
        self.output_dir = Path(output_dir)
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
        
        # 为每个说话人创建子目录
        self.speakers = set(item['speaker'] for item in subtitle_data)
        
        # 动态生成安全的目录名
        for speaker in self.speakers:
            safe_speaker = self.get_safe_speaker_name(speaker)
            (self.output_dir / safe_speaker).mkdir(exist_ok=True)
        
        print(f"检测到的说话人: {', '.join(self.speakers)}")
    
    def get_safe_speaker_name(self, speaker: str) -> str:
        """
        将说话人名称转换为安全的文件系统名称
        
        Args:
            speaker: 原始说话人名称
            
        Returns:
            安全的目录名称
        """
        import re
        import unicodedata
        
        # 首先尝试转换为ASCII（处理中文等非ASCII字符）
        # 如果包含中文，使用拼音或音译
        chinese_to_pinyin = {
            # 常用字符
            '女': 'nv', '主': 'zhu', '男': 'nan', '人': 'ren',
            '傅': 'fu', '总': 'zong', '老': 'lao', '板': 'ban',
            '儿': 'er', '媳': 'xi', '妇': 'fu', '子': 'zi',
            '护': 'hu', '士': 'shi', '医': 'yi', '生': 'sheng',
            '下': 'xia', '属': 'shu', '助': 'zhu', '理': 'li',
            '秘': 'mi', '书': 'shu', '经': 'jing', '理': 'li',
            '客': 'ke', '户': 'hu', '服': 'fu', '务': 'wu',
            '司': 'si', '机': 'ji', '保': 'bao', '安': 'an',
            '门': 'men', '卫': 'wei', '清': 'qing', '洁': 'jie',
            '工': 'gong', '员': 'yuan',
            # 新增常用字符
            '新': 'xin', '角': 'jiao', '色': 'se', '演': 'yan',
            '一': 'yi', '二': 'er', '三': 'san', '四': 'si', '五': 'wu',
            '六': 'liu', '七': 'qi', '八': 'ba', '九': 'jiu', '十': 'shi',
            '大': 'da', '小': 'xiao', '长': 'chang', '短': 'duan',
            '好': 'hao', '坏': 'huai', '美': 'mei', '丑': 'chou',
            '师': 'shi', '父': 'fu', '母': 'mu', '兄': 'xiong',
            '弟': 'di', '姐': 'jie', '妹': 'mei', '友': 'you',
            '同': 'tong', '学': 'xue', '班': 'ban', '级': 'ji',
            '王': 'wang', '李': 'li', '张': 'zhang', '刘': 'liu',
            '陈': 'chen', '杨': 'yang', '黄': 'huang', '赵': 'zhao',
            '周': 'zhou', '吴': 'wu', '徐': 'xu', '孙': 'sun',
            '马': 'ma', '朱': 'zhu', '胡': 'hu', '郭': 'guo',
            '何': 'he', '高': 'gao', '林': 'lin', '罗': 'luo'
        }
        
        # 转换中文字符
        result = ""
        for char in speaker:
            if char in chinese_to_pinyin:
                result += chinese_to_pinyin[char]
            elif char.isascii():
                result += char
            else:
                # 对于其他Unicode字符，尝试转换为ASCII
                try:
                    ascii_char = unicodedata.normalize('NFKD', char).encode('ascii', 'ignore').decode('ascii')
                    if ascii_char:
                        result += ascii_char
                    else:
                        result += 'x'  # 无法转换的字符用x代替
                except:
                    result += 'x'
        
        # 清理文件名：只保留字母、数字、下划线和连字符
        result = re.sub(r'[^a-zA-Z0-9_-]', '_', result)
        
        # 移除多余的下划线
        result = re.sub(r'_+', '_', result)
        result = result.strip('_')
        
        # 如果结果为空或太短，使用默认名称
        if not result or len(result) < 2:
            result = f"speaker_{hash(speaker) % 1000:03d}"
        
        # 限制长度
        if len(result) > 50:
            result = result[:50]
            
        return result.lower()
    
    def time_to_seconds(self, time_str: str) -> float:
        """
        将时间字符串转换为秒数
        
        Args:
            time_str: 时间字符串，格式如 "00:01:23.45"
            
        Returns:
            对应的秒数
        """
        # 解析时间格式 HH:MM:SS.MS
        time_parts = time_str.split(':')
        hours = int(time_parts[0])
        minutes = int(time_parts[1])
        seconds_parts = time_parts[2].split('.')
        seconds = int(seconds_parts[0])
        milliseconds = int(seconds_parts[1]) if len(seconds_parts) > 1 else 0
        
        total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 100.0
        return total_seconds
    
    def get_speaker_time_ranges(self) -> Dict[str, List[Tuple[float, float]]]:
        """
        获取每个说话人的时间范围
        
        Returns:
            字典，键为说话人，值为时间范围列表 [(start, end), ...]
        """
        speaker_ranges = {}
        
        for item in self.subtitle_data:
            speaker = item['speaker']
            start_time = self.time_to_seconds(item['start_time'])
            end_time = self.time_to_seconds(item['end_time'])
            
            if speaker not in speaker_ranges:
                speaker_ranges[speaker] = []
            
            speaker_ranges[speaker].append((start_time, end_time))
        
        # 对每个说话人的时间范围进行排序
        for speaker in speaker_ranges:
            speaker_ranges[speaker].sort()
        
        return speaker_ranges
    
    def is_time_in_speaker_range(self, timestamp: float, speaker_ranges: List[Tuple[float, float]]) -> bool:
        """
        检查时间戳是否在说话人的时间范围内
        
        Args:
            timestamp: 时间戳（秒）
            speaker_ranges: 说话人的时间范围列表
            
        Returns:
            是否在范围内
        """
        for start, end in speaker_ranges:
            if start <= timestamp <= end:
                return True
        return False
    
    def extract_frames(self, fps_extract: int = 1):
        """
        提取视频帧
        
        Args:
            fps_extract: 提取帧率，每秒提取多少帧
        """
        print(f"开始处理视频: {self.video_path}")
        
        # 打开视频文件
        cap = cv2.VideoCapture(self.video_path)
        if not cap.isOpened():
            raise ValueError(f"无法打开视频文件: {self.video_path}")
        
        # 获取视频信息
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = total_frames / fps
        
        print(f"视频信息:")
        print(f"  帧率: {fps:.2f} FPS")
        print(f"  总帧数: {total_frames}")
        print(f"  时长: {duration:.2f} 秒")
        print(f"  提取间隔: 每 {1/fps_extract:.2f} 秒提取一帧")
        
        # 获取说话人时间范围
        speaker_ranges = self.get_speaker_time_ranges()
        
        # 计算提取间隔（以帧为单位）
        frame_interval = int(fps / fps_extract)
        
        frame_count = 0
        extracted_count = {speaker: 0 for speaker in self.speakers}
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # 每隔指定帧数处理一次
                if frame_count % frame_interval == 0:
                    # 计算当前时间戳
                    timestamp = frame_count / fps
                    
                    # 检查当前时间戳属于哪个说话人
                    for speaker, ranges in speaker_ranges.items():
                        if self.is_time_in_speaker_range(timestamp, ranges):
                            # 保存帧
                            # 使用安全的文件名（避免中文字符问题）
                            safe_speaker = self.get_safe_speaker_name(speaker)
                            filename = f"{safe_speaker}_{timestamp:.2f}s_frame_{frame_count:06d}.jpg"
                            speaker_dir = self.output_dir / safe_speaker
                            speaker_dir.mkdir(exist_ok=True)  # 确保目录存在
                            filepath = speaker_dir / filename
                            
                            success = cv2.imwrite(str(filepath), frame)
                            if not success:
                                print(f"警告: 无法保存帧到 {filepath}")
                            else:
                                print(f"保存成功: {filepath}")
                            extracted_count[speaker] += 1
                            
                            print(f"提取帧: {speaker} - {timestamp:.2f}s")
                            break  # 一帧只属于一个说话人
                
                frame_count += 1
                
                # 显示进度
                if frame_count % (fps * 10) == 0:  # 每10秒显示一次进度
                    progress = (frame_count / total_frames) * 100
                    print(f"处理进度: {progress:.1f}% ({frame_count}/{total_frames} 帧)")
        
        finally:
            cap.release()
        
        # 输出统计信息
        print("\n提取完成!")
        print("各说话人提取的帧数:")
        total_extracted = 0
        for speaker, count in extracted_count.items():
            print(f"  {speaker}: {count} 帧")
            total_extracted += count
        print(f"总计提取: {total_extracted} 帧")
        
        return extracted_count


def load_subtitle_data(subtitle_file: str) -> List[Dict]:
    """
    加载字幕数据
    
    Args:
        subtitle_file: 字幕文件路径
        
    Returns:
        字幕数据列表
    """
    with open(subtitle_file, 'r', encoding='utf-8') as f:
        content = f.read().strip()
        
        # 如果内容以```json开始，去掉markdown格式
        if content.startswith('```json'):
            content = content[7:]  # 去掉```json
        if content.endswith('```'):
            content = content[:-3]  # 去掉```
        
        return json.loads(content)


def main():
    """主函数"""
    # 配置文件路径
    video_path = r"归档\chapter_003\ori_video.mp4"
    subtitle_file = "subtitle_output_raw.txt"
    output_dir = "extracted_frames_chapter_003"
    
    try:
        # 检查文件是否存在
        if not os.path.exists(video_path):
            print(f"错误: 视频文件不存在: {video_path}")
            return
        
        if not os.path.exists(subtitle_file):
            print(f"错误: 字幕文件不存在: {subtitle_file}")
            return
        
        # 加载字幕数据
        print("加载字幕数据...")
        subtitle_data = load_subtitle_data(subtitle_file)
        print(f"加载了 {len(subtitle_data)} 条字幕记录")
        
        # 创建提取器并开始提取
        extractor = VideoFrameExtractor(video_path, subtitle_data, output_dir)
        
        # 每秒提取1帧
        extractor.extract_frames(fps_extract=1)
        
        print(f"\n所有帧已保存到: {output_dir}")
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
