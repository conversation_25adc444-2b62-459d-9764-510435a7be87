#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频字幕合成工具
将MP4视频文件与SRT字幕文件合成为带字幕的视频
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path
from typing import Optional, Tuple
import json

class VideoSubtitleMerger:
    def __init__(self):
        self.ffmpeg_path = self._find_ffmpeg()
        
    def _find_ffmpeg(self) -> str:
        """查找FFmpeg可执行文件路径"""
        try:
            # 尝试直接调用ffmpeg
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=5)
            if result.returncode == 0:
                return 'ffmpeg'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        # 检查常见安装路径
        common_paths = [
            'ffmpeg',
            'C:\\ffmpeg\\bin\\ffmpeg.exe',
            'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
            '/usr/local/bin/ffmpeg',
            '/usr/bin/ffmpeg'
        ]
        
        for path in common_paths:
            if os.path.exists(path) or path == 'ffmpeg':
                try:
                    result = subprocess.run([path, '-version'], 
                                          capture_output=True, 
                                          text=True, 
                                          timeout=5)
                    if result.returncode == 0:
                        return path
                except:
                    continue
        
        raise RuntimeError("未找到FFmpeg，请确保FFmpeg已安装并添加到PATH环境变量中")
    
    def validate_files(self, video_path: str, subtitle_path: str) -> Tuple[bool, str]:
        """验证输入文件"""
        if not os.path.exists(video_path):
            return False, f"视频文件不存在: {video_path}"
        
        if not os.path.exists(subtitle_path):
            return False, f"字幕文件不存在: {subtitle_path}"
        
        # 检查文件扩展名
        video_ext = os.path.splitext(video_path)[1].lower()
        subtitle_ext = os.path.splitext(subtitle_path)[1].lower()
        
        if video_ext not in ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv']:
            return False, f"不支持的视频格式: {video_ext}"
        
        if subtitle_ext not in ['.srt', '.ass', '.ssa', '.vtt']:
            return False, f"不支持的字幕格式: {subtitle_ext}"
        
        return True, "文件验证通过"
    
    def merge_subtitle(self, 
                      video_path: str, 
                      subtitle_path: str, 
                      output_path: Optional[str] = None,
                      font_size: int = 20,
                      font_color: str = "white",
                      outline_color: str = "black",
                      outline_width: int = 1,
                      position: str = "bottom") -> bool:
        """
        合成视频字幕
        
        Args:
            video_path: 输入视频文件路径
            subtitle_path: 字幕文件路径
            output_path: 输出视频路径（可选）
            font_size: 字体大小
            font_color: 字体颜色
            outline_color: 字体轮廓颜色
            outline_width: 字体轮廓宽度
            position: 字幕位置 (bottom, top, center)
        
        Returns:
            bool: 合成是否成功
        """
        # 验证输入文件
        is_valid, message = self.validate_files(video_path, subtitle_path)
        if not is_valid:
            print(f"错误: {message}")
            return False
        
        # 生成输出文件名
        if output_path is None:
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            output_path = f"{video_name}_with_subtitles.mp4"
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 构建FFmpeg命令
        subtitle_filter = self._build_subtitle_filter(
            subtitle_path, font_size, font_color, outline_color, outline_width, position
        )
        
        cmd = [
            self.ffmpeg_path,
            '-i', video_path,
            '-vf', subtitle_filter,
            '-c:a', 'copy',  # 音频直接复制，不重新编码
            '-c:v', 'libx264',  # 使用H.264编码器
            '-preset', 'medium',  # 编码速度预设
            '-crf', '23',  # 质量控制
            '-y',  # 覆盖输出文件
            output_path
        ]
        
        print(f"开始合成视频字幕...")
        print(f"输入视频: {video_path}")
        print(f"字幕文件: {subtitle_path}")
        print(f"输出文件: {output_path}")
        
        try:
            # 执行FFmpeg命令
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8'
            )
            
            # 实时显示进度
            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break
                if output and 'time=' in output:
                    # 提取时间信息显示进度
                    time_info = [x for x in output.split() if 'time=' in x]
                    if time_info:
                        print(f"\r进度: {time_info[0]}", end='', flush=True)
            
            # 获取返回码
            return_code = process.poll()
            
            if return_code == 0:
                print(f"\n✅ 字幕合成成功!")
                print(f"输出文件: {output_path}")
                return True
            else:
                stderr_output = process.stderr.read()
                print(f"\n❌ 字幕合成失败!")
                print(f"错误信息: {stderr_output}")
                return False
                
        except Exception as e:
            print(f"❌ 执行过程中出现错误: {str(e)}")
            return False
    
    def _build_subtitle_filter(self, 
                              subtitle_path: str, 
                              font_size: int,
                              font_color: str,
                              outline_color: str,
                              outline_width: int,
                              position: str) -> str:
        """构建字幕滤镜字符串"""
        # 转义文件路径中的特殊字符
        escaped_path = subtitle_path.replace('\\', '\\\\').replace(':', '\\:')
        
        # 位置映射
        position_map = {
            'bottom': 'Alignment=2',  # 底部居中
            'top': 'Alignment=8',     # 顶部居中
            'center': 'Alignment=5'   # 中心
        }
        
        alignment = position_map.get(position, 'Alignment=2')
        
        # 构建字幕滤镜
        subtitle_filter = (
            f"subtitles='{escaped_path}'"
            f":force_style='"
            f"FontSize={font_size},"
            f"PrimaryColour=&H{self._color_to_hex(font_color)},"
            f"OutlineColour=&H{self._color_to_hex(outline_color)},"
            f"Outline={outline_width},"
            f"{alignment}"
            f"'"
        )
        
        return subtitle_filter
    
    def _color_to_hex(self, color: str) -> str:
        """将颜色名称转换为十六进制格式（BGR格式用于ASS）"""
        color_map = {
            'white': 'FFFFFF',
            'black': '000000',
            'red': '0000FF',
            'green': '00FF00',
            'blue': 'FF0000',
            'yellow': '00FFFF',
            'cyan': 'FFFF00',
            'magenta': 'FF00FF'
        }
        
        return color_map.get(color.lower(), 'FFFFFF')
    
    def batch_merge(self, input_dir: str, output_dir: Optional[str] = None) -> int:
        """
        批量合成字幕
        
        Args:
            input_dir: 输入目录，应包含MP4和SRT文件
            output_dir: 输出目录
        
        Returns:
            int: 成功处理的文件数量
        """
        if not os.path.exists(input_dir):
            print(f"错误: 输入目录不存在: {input_dir}")
            return 0
        
        if output_dir is None:
            output_dir = os.path.join(input_dir, 'output')
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 查找匹配的视频和字幕文件
        video_files = []
        subtitle_files = []
        
        for file in os.listdir(input_dir):
            if file.lower().endswith(('.mp4', '.avi', '.mkv', '.mov')):
                video_files.append(file)
            elif file.lower().endswith(('.srt', '.ass', '.ssa', '.vtt')):
                subtitle_files.append(file)
        
        # 匹配文件对
        successful_count = 0
        
        for video_file in video_files:
            video_name = os.path.splitext(video_file)[0]
            
            # 查找匹配的字幕文件
            matching_subtitle = None
            for subtitle_file in subtitle_files:
                subtitle_name = os.path.splitext(subtitle_file)[0]
                if subtitle_name == video_name or subtitle_name in video_name:
                    matching_subtitle = subtitle_file
                    break
            
            if matching_subtitle:
                video_path = os.path.join(input_dir, video_file)
                subtitle_path = os.path.join(input_dir, matching_subtitle)
                output_path = os.path.join(output_dir, f"{video_name}_with_subtitles.mp4")
                
                print(f"\n处理: {video_file} + {matching_subtitle}")
                if self.merge_subtitle(video_path, subtitle_path, output_path):
                    successful_count += 1
                else:
                    print(f"❌ 处理失败: {video_file}")
            else:
                print(f"⚠️  未找到匹配的字幕文件: {video_file}")
        
        print(f"\n批量处理完成! 成功处理 {successful_count} 个文件")
        return successful_count


def main():
    parser = argparse.ArgumentParser(description='视频字幕合成工具')
    parser.add_argument('--video', '-v', help='输入视频文件路径')
    parser.add_argument('--subtitle', '-s', help='字幕文件路径')
    parser.add_argument('--output', '-o', help='输出视频文件路径')
    parser.add_argument('--batch', '-b', help='批量处理目录')
    parser.add_argument('--font-size', type=int, default=20, help='字体大小 (默认: 20)')
    parser.add_argument('--font-color', default='white', help='字体颜色 (默认: white)')
    parser.add_argument('--outline-color', default='black', help='字体轮廓颜色 (默认: black)')
    parser.add_argument('--outline-width', type=int, default=1, help='字体轮廓宽度 (默认: 1)')
    parser.add_argument('--position', choices=['bottom', 'top', 'center'], 
                       default='bottom', help='字幕位置 (默认: bottom)')
    
    args = parser.parse_args()
    
    merger = VideoSubtitleMerger()
    
    try:
        if args.batch:
            # 批量处理模式
            merger.batch_merge(args.batch)
        elif args.video and args.subtitle:
            # 单文件处理模式
            success = merger.merge_subtitle(
                video_path=args.video,
                subtitle_path=args.subtitle,
                output_path=args.output,
                font_size=args.font_size,
                font_color=args.font_color,
                outline_color=args.outline_color,
                outline_width=args.outline_width,
                position=args.position
            )
            
            if not success:
                sys.exit(1)
        else:
            # 交互式模式
            print("=== 视频字幕合成工具 ===")
            print("1. 单文件合成")
            print("2. 批量合成")
            
            choice = input("请选择模式 (1/2): ").strip()
            
            if choice == '1':
                video_path = input("请输入视频文件路径: ").strip()
                subtitle_path = input("请输入字幕文件路径: ").strip()
                output_path = input("请输入输出文件路径 (回车使用默认): ").strip() or None
                
                merger.merge_subtitle(video_path, subtitle_path, output_path)
                
            elif choice == '2':
                input_dir = input("请输入包含视频和字幕文件的目录: ").strip()
                merger.batch_merge(input_dir)
            else:
                print("无效选择")
                sys.exit(1)
                
    except KeyboardInterrupt:
        print("\n用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行错误: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
