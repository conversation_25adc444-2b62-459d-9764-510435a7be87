#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频字幕合并与角色识别工具
简化版本 - 移除冗余输出，专注核心功能
"""

import os
import re
import json
import subprocess
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from google import genai
from google.genai.types import GenerateContentConfig, Part

# 导入分割字幕功能
from split_subtitles_by_episodes import (
    split_subtitles_by_episodes,
    create_summary,
    load_timeline_info,
    load_subtitle_data
)

# Gemini配置
PROJECT_ID = "gen-lang-client-0255558294"
LOCATION = os.environ.get("GOOGLE_CLOUD_REGION", "global")
client = genai.Client(vertexai=True, project=PROJECT_ID, location=LOCATION)
MODEL_ID = "gemini-2.5-pro"

def get_audio_duration(audio_file: str) -> float:
    """获取音频文件时长（秒）"""
    try:
        cmd = ['ffprobe', '-v', 'quiet', '-print_format', 'json', '-show_format', str(audio_file)]
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
        if result.returncode == 0:
            return float(json.loads(result.stdout)['format']['duration'])
        return 0.0
    except Exception:
        return 0.0

def parse_srt_time(time_str: str) -> float:
    """SRT时间格式转秒数"""
    time_str = time_str.replace(',', '.')
    parts = time_str.split(':')
    return int(parts[0]) * 3600 + int(parts[1]) * 60 + float(parts[2])

def format_srt_time(seconds: float) -> str:
    """秒数转SRT时间格式"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')

def parse_srt_file(file_path: str) -> List[Dict]:
    """解析SRT文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
    except UnicodeDecodeError:
        with open(file_path, 'r', encoding='gbk') as f:
            content = f.read().strip()
    
    subtitles = []
    for block in content.split('\n\n'):
        if not block.strip():
            continue
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue
        
        time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', lines[1])
        if time_match:
            subtitles.append({
                'index': int(lines[0]),
                'start_time': time_match.group(1),
                'end_time': time_match.group(2),
                'start_seconds': parse_srt_time(time_match.group(1)),
                'end_seconds': parse_srt_time(time_match.group(2)),
                'text': '\n'.join(lines[2:])
            })
    return subtitles

def concatenate_audio_files(audio_files: List[Path], output_file: str) -> bool:
    """拼接音频文件"""
    try:
        filelist_path = "temp_audio_filelist.txt"
        with open(filelist_path, 'w', encoding='utf-8') as f:
            for audio_file in audio_files:
                escaped_path = str(audio_file).replace("'", "'\"'\"'")
                f.write(f"file '{escaped_path}'\n")

        cmd = ['ffmpeg', '-f', 'concat', '-safe', '0', '-i', filelist_path, '-c', 'copy', '-y', str(output_file)]
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')

        if os.path.exists(filelist_path):
            os.remove(filelist_path)

        return result.returncode == 0
    except Exception:
        return False

def merge_subtitles(subtitle_files: List[Tuple[str, float]], output_file: str) -> bool:
    """合并字幕文件"""
    try:
        all_subtitles = []
        current_offset = 0.0
        subtitle_index = 1

        for srt_file, duration in subtitle_files:
            subtitles = parse_srt_file(srt_file)
            for sub in subtitles:
                all_subtitles.append({
                    'index': subtitle_index,
                    'start_time': format_srt_time(sub['start_seconds'] + current_offset),
                    'end_time': format_srt_time(sub['end_seconds'] + current_offset),
                    'text': sub['text']
                })
                subtitle_index += 1
            current_offset += duration

        with open(output_file, 'w', encoding='utf-8') as f:
            for sub in all_subtitles:
                f.write(f"{sub['index']}\n{sub['start_time']} --> {sub['end_time']}\n{sub['text']}\n\n")

        return True
    except Exception:
        return False

def get_chapter_folders(archive_dir: str) -> List[str]:
    """获取章节文件夹列表"""
    archive_path = Path(archive_dir)
    if not archive_path.exists():
        return []
    
    chapter_folders = [item.name for item in archive_path.iterdir() 
                      if item.is_dir() and item.name.startswith('chapter_')]
    
    def extract_chapter_number(folder_name):
        match = re.search(r'chapter_(\d+)', folder_name)
        return int(match.group(1)) if match else 0
    
    return sorted(chapter_folders, key=extract_chapter_number)

def gemini_role_analysis(audio_url: str, srt_content: str, output_file: str) -> bool:
    """使用Gemini分析角色"""
    try:
        prompt = f"""根据这段音频，参考提供的srt字幕，识别音频中的说话人，台词。返回字段包含speaker, start_time, end_time, gender, text。返回json格式。

**要求**：
1. 不要修改参考SRT文件内容的时间轴。时间轴完全和原来的字幕保持完全一致。
2. 根据上下文识别speaker，要求speaker能做到上下文合理。speaker需要为中文。
3. start_time, end_time格式为00:14:03,557，注意逗号是英文逗号。
4. gender为男或女。根据音频识别说话人的性别。

*参考的SRT文件内容：
{srt_content}

请根据音频内容生成准确的字幕信息。"""

        response = client.models.generate_content(
            model=MODEL_ID,
            contents=[
                Part.from_uri(file_uri=audio_url, mime_type="audio/mpeg"),
                prompt,
            ],
            config=GenerateContentConfig(audio_timestamp=True, temperature=1.0),
        )

        try:
            # 先尝试直接解析JSON
            json_data = json.loads(response.text)
        except json.JSONDecodeError:
            # 如果失败，尝试处理markdown格式的JSON
            content = response.text.strip()
            
            # 移除markdown代码块标记
            if content.startswith('```json'):
                content = content[7:]
            elif content.startswith('```'):
                content = content[3:]
                
            if content.endswith('```'):
                content = content[:-3]
            
            content = content.strip()
            
            try:
                json_data = json.loads(content)
            except json.JSONDecodeError:
                # 如果还是失败，保存原始响应
                with open(f"{output_file}_raw.txt", 'w', encoding='utf-8') as f:
                    f.write(response.text)
                print(f"⚠️ JSON解析失败，原始响应已保存")
                return False

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        print(f"✅ Gemini角色分析完成: {output_file}")
        return True
    except Exception as e:
        print(f"❌ Gemini分析失败: {e}")
        return False

def compare_subtitles(original_srt: str, gemini_json: str) -> Dict:
    """比对原始SRT和Gemini输出的JSON字幕"""
    try:
        # 读取原始SRT
        original_subtitles = []
        with open(original_srt, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        for block in content.split('\n\n'):
            if not block.strip():
                continue
            lines = block.strip().split('\n')
            if len(lines) < 3:
                continue
            
            time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', lines[1])
            if time_match:
                original_subtitles.append({
                    'index': int(lines[0]),
                    'start_time': time_match.group(1),
                    'end_time': time_match.group(2),
                    'text': '\n'.join(lines[2:])
                })
        
        # 读取Gemini JSON
        with open(gemini_json, 'r', encoding='utf-8') as f:
            gemini_data = json.load(f)
        
        # 比对结果
        comparison = {
            'original_count': len(original_subtitles),
            'gemini_count': len(gemini_data),
            'timeline_matches': 0,
            'text_matches': 0,
            'differences': []
        }
        
        print(f"\n📊 字幕比对结果:")
        print(f"   原始字幕数: {comparison['original_count']}")
        print(f"   Gemini字幕数: {comparison['gemini_count']}")
        
        # 创建时间轴映射
        gemini_by_time = {}
        for item in gemini_data:
            key = f"{item['start_time']} --> {item['end_time']}"
            gemini_by_time[key] = item
        
        # 逐条比对
        for orig in original_subtitles:
            time_key = f"{orig['start_time']} --> {orig['end_time']}"
            
            if time_key in gemini_by_time:
                comparison['timeline_matches'] += 1
                gemini_item = gemini_by_time[time_key]
                
                # 比对文本内容
                orig_text = orig['text'].strip()
                gemini_text = gemini_item['text'].strip()
                
                if orig_text == gemini_text:
                    comparison['text_matches'] += 1
                else:
                    comparison['differences'].append({
                        'timeline': time_key,
                        'original': orig_text,
                        'gemini': gemini_text,
                        'speaker': gemini_item.get('speaker', '未知'),
                        'gender': gemini_item.get('gender', '未知')
                    })
        
        # 计算匹配率
        timeline_match_rate = (comparison['timeline_matches'] / comparison['original_count'] * 100) if comparison['original_count'] > 0 else 0
        comparison['timeline_match_rate'] = timeline_match_rate

        # 输出统计
        print(f"   时间轴匹配: {comparison['timeline_matches']}/{comparison['original_count']} ({timeline_match_rate:.1f}%)")
        print(f"   文本匹配: {comparison['text_matches']}/{comparison['timeline_matches']}")
        print(f"   差异条目: {len(comparison['differences'])}")

        # 保存比对结果
        comparison_file = gemini_json.replace('.json', '_comparison.json')
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison, f, ensure_ascii=False, indent=2)
        print(f"   比对详情已保存: {comparison_file}")

        # 显示部分差异示例
        if comparison['differences']:
            print(f"\n📝 差异示例 (前3条):")
            for i, diff in enumerate(comparison['differences'][:3]):
                print(f"   {i+1}. {diff['timeline']}")
                print(f"      原始: {diff['original']}")
                print(f"      Gemini: {diff['gemini']} [{diff['speaker']}/{diff['gender']}]")

        return comparison
        
    except Exception as e:
        print(f"❌ 字幕比对失败: {e}")
        return {}

def generate_speaker_srt(gemini_json: str, output_file: str) -> bool:
    """根据Gemini JSON生成带说话人的SRT文件"""
    try:
        with open(gemini_json, 'r', encoding='utf-8') as f:
            gemini_data = json.load(f)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for i, item in enumerate(gemini_data, 1):
                f.write(f"{i}\n")
                f.write(f"{item['start_time']} --> {item['end_time']}\n")
                speaker = item.get('speaker', '未知')
                gender = item.get('gender', '')
                gender_mark = f"({gender})" if gender else ""
                f.write(f"[{speaker}{gender_mark}] {item['text']}\n\n")
        
        print(f"✅ 带说话人SRT已生成: {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 生成带说话人SRT失败: {e}")
        return False

def execute_subtitle_splitting(gemini_json: str, timeline_file: str = "merge_timeline.json", output_dir: str = "episodes_subtitles") -> bool:
    """执行字幕分割功能"""
    try:
        print(f"\n🎬 开始执行字幕分割...")
        print(f"   📄 字幕文件: {gemini_json}")
        print(f"   ⏱️  时间轴文件: {timeline_file}")
        print(f"   📁 输出目录: {output_dir}")

        # 检查时间轴文件是否存在
        if not os.path.exists(timeline_file):
            print(f"❌ 时间轴文件不存在: {timeline_file}")
            print("   请确保存在时间轴文件才能执行分割功能")
            return False

        # 1. 加载时间轴信息
        print("\n1️⃣ 加载时间轴信息...")
        timeline_info = load_timeline_info(timeline_file)
        if not timeline_info:
            print("❌ 无法加载时间轴信息")
            return False
        print(f"   ✅ 加载了 {len(timeline_info)} 个分集的时间信息")

        # 2. 加载字幕数据
        print("\n2️⃣ 加载字幕数据...")
        subtitle_data = load_subtitle_data(gemini_json)
        if not subtitle_data:
            print("❌ 无法加载字幕数据")
            return False
        print(f"   ✅ 加载了 {len(subtitle_data)} 条字幕")

        # 3. 按分集分割字幕
        print("\n3️⃣ 按分集分割字幕...")
        split_subtitles_by_episodes(subtitle_data, timeline_info, output_dir)

        # 4. 创建总结报告
        print("\n4️⃣ 创建总结报告...")
        create_summary(subtitle_data, timeline_info, output_dir)

        print(f"\n✅ 字幕分割完成! 所有文件已保存到 {output_dir} 目录中")
        return True

    except Exception as e:
        print(f"❌ 字幕分割失败: {e}")
        return False

def main():
    print("🎵 归档音频字幕合并 + Gemini角色识别工具")
    print("=" * 60)
    
    # 配置
    archive_dir = "归档"
    output_audio = "merged_all_chapters.wav"
    output_subtitle = "merged_all_chapters.srt"
    gemini_output = "gemini_role_analysis.json"
    speaker_srt = "merged_all_chapters_with_speaker.srt"
    audio_url = "https://gidfyoqecthbormpbirt.supabase.co/storage/v1/object/public/audios/merged_0902.mp3"
    
    # 检查FFmpeg
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        print("✅ FFmpeg检测成功")
    except:
        print("❌ 请确保安装了完整的FFmpeg套件")
        return 1
    
    # 获取章节目录
    chapter_folders = get_chapter_folders(archive_dir)
    if not chapter_folders:
        print(f"❌ 在 {archive_dir} 目录下未找到章节文件夹")
        return 1
    
    print(f"📁 找到 {len(chapter_folders)} 个章节")
    
    # 收集音频和字幕文件
    audio_files = []
    subtitle_files = []
    
    for folder in chapter_folders:
        chapter_path = Path(archive_dir) / folder
        audio_file = chapter_path / "ori_audio.wav"
        srt_file = chapter_path / "ori.srt"
        
        if audio_file.exists() and srt_file.exists():
            duration = get_audio_duration(audio_file)
            if duration > 0:
                audio_files.append(audio_file)
                subtitle_files.append((str(srt_file), duration))
                print(f"   ✅ {folder}: {duration:.2f}s")
    
    if not audio_files:
        print("❌ 未找到有效的音频文件")
        return 1
    
    # 合并音频
    print("\n🎵 开始合并音频...")
    if concatenate_audio_files(audio_files, output_audio):
        print(f"   ✅ 音频合并完成: {output_audio}")
    else:
        print("❌ 音频合并失败")
        return 1
    
    # 合并字幕
    print("\n📄 开始合并字幕...")
    if merge_subtitles(subtitle_files, output_subtitle):
        print(f"   ✅ 字幕合并完成: {output_subtitle}")
    else:
        print("❌ 字幕合并失败")
        return 1
    
    # Gemini角色分析
    print("\n🤖 开始Gemini角色分析...")
    with open(output_subtitle, 'r', encoding='utf-8') as f:
        srt_content = f.read()
    
    if gemini_role_analysis(audio_url, srt_content, gemini_output):
        # 比对字幕
        print("\n🔍 开始字幕比对...")
        comparison_result = compare_subtitles(output_subtitle, gemini_output)

        # 生成带说话人的SRT
        print("\n📝 生成带说话人字幕...")
        generate_speaker_srt(gemini_output, speaker_srt)

        # 检查时间轴匹配率
        if comparison_result and 'timeline_match_rate' in comparison_result:
            match_rate = comparison_result['timeline_match_rate']
            print(f"\n📊 时间轴匹配率: {match_rate:.1f}%")

            if match_rate == 100.0:
                print("🎯 时间轴匹配率为100%，开始执行字幕分割...")
                if execute_subtitle_splitting(gemini_output):
                    print("✅ 字幕分割执行成功!")
                else:
                    print("❌ 字幕分割执行失败")
            else:
                print(f"⚠️  时间轴匹配率为 {match_rate:.1f}%，未达到100%")
                print("💡 建议:")
                print("   1. 检查音频和字幕是否完全对应")
                print("   2. 重新运行Gemini分析")
                print("   3. 手动检查差异项目")
                print("   4. 如需强制执行分割，请手动运行 split_subtitles_by_episodes.py")
        else:
            print("⚠️  无法获取匹配率信息，请检查比对结果")
    
    print("\n🎉 处理完成!")
    print(f"   🎵 输出音频: {output_audio}")
    print(f"   📄 输出字幕: {output_subtitle}")
    print(f"   🤖 角色分析: {gemini_output}")
    print(f"   👥 带说话人字幕: {speaker_srt}")

if __name__ == "__main__":
    main()

