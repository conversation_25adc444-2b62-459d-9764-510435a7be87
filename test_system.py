#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试InsightFace人脸提取系统
"""

import os
import sys
from pathlib import Path

def test_imports():
    """测试所有必要的包是否都能正常导入"""
    print("测试包导入...")
    
    try:
        import cv2
        print("✓ OpenCV 导入成功")
    except ImportError as e:
        print(f"✗ OpenCV 导入失败: {e}")
        return False
    
    try:
        import insightface
        print("✓ InsightFace 导入成功")
    except ImportError as e:
        print(f"✗ InsightFace 导入失败: {e}")
        return False
    
    try:
        import numpy as np
        print("✓ NumPy 导入成功")
    except ImportError as e:
        print(f"✗ NumPy 导入失败: {e}")
        return False
    
    try:
        from sklearn.cluster import DBSCAN
        print("✓ Scikit-learn 导入成功")
    except ImportError as e:
        print(f"✗ Scikit-learn 导入失败: {e}")
        return False
    
    try:
        import matplotlib.pyplot as plt
        print("✓ Matplotlib 导入成功")
    except ImportError as e:
        print(f"✗ Matplotlib 导入失败: {e}")
        return False
    
    return True

def test_insightface_init():
    """测试InsightFace初始化"""
    print("\n测试InsightFace初始化...")
    
    try:
        from insightface.app import FaceAnalysis
        
        # 初始化InsightFace (这可能需要下载模型)
        app = FaceAnalysis(providers=['CPUExecutionProvider'])
        app.prepare(ctx_id=0, det_size=(320, 320))  # 使用较小尺寸测试
        
        print("✓ InsightFace 初始化成功")
        return True
        
    except Exception as e:
        print(f"✗ InsightFace 初始化失败: {e}")
        print("提示: 首次运行时，InsightFace 需要下载模型文件，这可能需要一些时间")
        return False

def check_input_files():
    """检查输入文件是否存在"""
    print("\n检查输入文件...")
    
    test_files = [
        (r"归档\chapter_003\ori_video.mp4", "测试视频文件"),
        (r"归档\chapter_003\matched_subtitles.json", "测试字幕文件")
    ]
    
    all_exist = True
    for file_path, description in test_files:
        if os.path.exists(file_path):
            print(f"✓ {description} 存在: {file_path}")
        else:
            print(f"✗ {description} 不存在: {file_path}")
            all_exist = False
    
    return all_exist

def test_core_functions():
    """测试核心功能模块"""
    print("\n测试核心功能...")
    
    try:
        from insightface_video_processor import InsightFaceVideoProcessor
        
        # 创建临时处理器 (不初始化InsightFace)
        print("✓ 核心类导入成功")
        
        # 测试工具函数
        processor = InsightFaceVideoProcessor("test_output")
        
        # 测试时间转换
        time_seconds = processor.time_to_seconds("00:01:23,456")
        expected = 1*60 + 23 + 456/1000.0
        if abs(time_seconds - expected) < 0.001:
            print("✓ 时间转换功能正常")
        else:
            print(f"✗ 时间转换错误: 期望 {expected}, 得到 {time_seconds}")
        
        # 测试文件名安全化
        safe_name = processor.get_safe_speaker_name("说话人_1")
        print(f"✓ 文件名安全化正常: '说话人_1' -> '{safe_name}'")
        
        return True
        
    except Exception as e:
        print(f"✗ 核心功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== InsightFace 人脸提取系统测试 ===\n")
    
    tests = [
        ("包导入测试", test_imports),
        ("输入文件检查", check_input_files), 
        ("核心功能测试", test_core_functions),
        ("InsightFace初始化测试", test_insightface_init),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"\n🎉 {test_name} 通过")
                passed += 1
            else:
                print(f"\n❌ {test_name} 失败")
        except Exception as e:
            print(f"\n💥 {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过! 系统可以正常使用")
        print("\n下一步:")
        print("1. 运行 'python run_face_extraction.py' 开始处理")
        print("2. 或运行 'python advanced_face_extraction.py --interactive' 使用高级功能")
    else:
        print("❌ 部分测试失败，请检查上述错误信息")
        print("\n建议:")
        print("1. 确保所有依赖包都已正确安装")
        print("2. 检查网络连接（InsightFace首次运行需下载模型）")
        print("3. 确保输入文件路径正确")

if __name__ == "__main__":
    main()
