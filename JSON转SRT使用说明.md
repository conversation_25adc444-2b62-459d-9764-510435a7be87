# JSON字幕转SRT格式转换器

## 功能介绍

这个脚本可以将JSON格式的字幕数据转换为标准的SRT字幕文件格式。支持包含或不包含说话人信息的转换。

## 文件说明

- `json_to_srt_converter.py` - 主转换脚本
- `convert_example.py` - 使用示例脚本
- `JSON转SRT使用说明.md` - 本说明文档

## 使用方法

### 方法1: 直接运行转换器

```bash
# 转换默认文件 subtitle_output_raw.txt
python json_to_srt_converter.py

# 指定输入文件
python json_to_srt_converter.py input.txt

# 指定输入和输出文件
python json_to_srt_converter.py input.txt -o output.srt

# 不包含说话人信息
python json_to_srt_converter.py input.txt --no-speaker
```

### 方法2: 在Python代码中使用

```python
from json_to_srt_converter import json_to_srt

# 包含说话人信息（默认）
json_to_srt("subtitle_output_raw.txt", "output_with_speaker.srt")

# 不包含说话人信息
json_to_srt("subtitle_output_raw.txt", "output_without_speaker.srt", include_speaker=False)
```

### 方法3: 运行示例脚本

```bash
python convert_example.py
```

## 输入格式

JSON文件应包含以下格式的数据：

```json
[
  {
    "speaker": "女主",
    "start_time": "00:00:01.30",
    "end_time": "00:00:03.34",
    "text": "我没死，我没死。"
  },
  {
    "speaker": "傅总",
    "start_time": "00:00:18.44",
    "end_time": "00:00:19.12",
    "text": "你谁啊？"
  }
]
```

## 输出格式

### 包含说话人信息的SRT文件：

```
1
00:00:01,300 --> 00:00:03,340
女主: 我没死，我没死。

2
00:00:18,440 --> 00:00:19,120
傅总: 你谁啊？
```

### 不包含说话人信息的SRT文件：

```
1
00:00:01,300 --> 00:00:03,340
我没死，我没死。

2
00:00:18,440 --> 00:00:19,120
你谁啊？
```

## 支持的时间格式

- `HH:MM:SS.mmm` (如: 01:23:45.678)
- `MM:SS.mmm` (如: 23:45.678)

## 特性

1. **自动处理markdown代码块** - 支持包含 ```json 标记的文件
2. **灵活的时间格式** - 支持多种时间格式输入
3. **说话人选项** - 可选择是否包含说话人信息
4. **错误处理** - 完整的错误处理和用户友好的错误信息
5. **自动文件命名** - 如果不指定输出文件名，会自动生成

## 生成的文件示例

基于您的 `subtitle_output_raw.txt` 文件，脚本已生成：

- `subtitle_output_raw.srt` - 默认转换（包含说话人）
- `output_with_speaker.srt` - 包含说话人信息版本
- `output_without_speaker.srt` - 纯字幕版本

## 注意事项

1. 确保JSON文件编码为UTF-8
2. 时间格式必须正确，支持小数秒
3. 脚本会自动处理可能的markdown代码块标记
4. 输出的SRT文件使用逗号作为毫秒分隔符（标准SRT格式）

