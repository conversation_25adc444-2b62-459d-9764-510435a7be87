#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕文件拼接脚本
功能：将归档文件夹中的所有章节字幕文件按时间轴拼接成一个完整的字幕文件
"""

import os
import re
import wave
from pathlib import Path
from typing import List, Tuple
import argparse

def parse_srt_time(time_str: str) -> float:
    """
    将SRT时间格式转换为秒数
    格式: HH:MM:SS,mmm
    """
    # 替换逗号为点号
    time_str = time_str.replace(',', '.')
    
    # 分割时间组件
    parts = time_str.split(':')
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = float(parts[2])
    
    total_seconds = hours * 3600 + minutes * 60 + seconds
    return total_seconds

def format_srt_time(seconds: float) -> str:
    """
    将秒数转换为SRT时间格式
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    
    # 格式化为 HH:MM:SS,mmm
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')

def get_audio_duration(audio_path: str) -> float:
    """
    获取音频文件的时长（秒）
    """
    try:
        with wave.open(audio_path, 'rb') as audio_file:
            frames = audio_file.getnframes()
            sample_rate = audio_file.getframerate()
            duration = frames / float(sample_rate)
            return duration
    except Exception as e:
        print(f"警告：无法读取音频文件 {audio_path} 的时长: {e}")
        return 0.0

def parse_srt_file(file_path: str) -> List[dict]:
    """
    解析SRT文件
    """
    subtitles = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read().strip()
        except:
            print(f"警告：无法读取文件 {file_path}")
            return []
    
    # 分割字幕块
    blocks = content.split('\n\n')
    
    for block in blocks:
        if not block.strip():
            continue
            
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue
            
        # 解析序号
        try:
            index = int(lines[0])
        except:
            continue
            
        # 解析时间轴
        time_line = lines[1]
        time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', time_line)
        if not time_match:
            continue
            
        start_time = time_match.group(1)
        end_time = time_match.group(2)
        
        # 解析字幕文本
        text = '\n'.join(lines[2:])
        
        subtitles.append({
            'index': index,
            'start_time': start_time,
            'end_time': end_time,
            'start_seconds': parse_srt_time(start_time),
            'end_seconds': parse_srt_time(end_time),
            'text': text
        })
    
    return subtitles

def concatenate_subtitles(archive_dir: str, output_file: str = 'combined.srt'):
    """
    拼接所有章节的字幕文件
    """
    archive_path = Path(archive_dir)
    if not archive_path.exists():
        print(f"错误：归档目录 {archive_dir} 不存在")
        return
    
    # 获取所有章节目录
    chapter_dirs = []
    for item in archive_path.iterdir():
        if item.is_dir() and item.name.startswith('chapter_'):
            chapter_dirs.append(item)
    
    # 按章节编号排序
    chapter_dirs.sort(key=lambda x: int(x.name.split('_')[1]))
    
    print(f"找到 {len(chapter_dirs)} 个章节目录")
    
    combined_subtitles = []
    current_time_offset = 0.0
    subtitle_index = 0
    
    for chapter_dir in chapter_dirs:
        chapter_name = chapter_dir.name
        print(f"处理章节: {chapter_name}")
        
        # 查找SRT和音频文件
        srt_file = chapter_dir / 'ori.srt'
        audio_file = chapter_dir / 'ori_audio.wav'
        
        if not srt_file.exists():
            print(f"警告：章节 {chapter_name} 中未找到 ori.srt 文件")
            continue
        
        # 解析字幕
        subtitles = parse_srt_file(str(srt_file))
        if not subtitles:
            print(f"警告：章节 {chapter_name} 的字幕文件为空或解析失败")
            continue
        
        print(f"  - 找到 {len(subtitles)} 条字幕")
        
        # 调整时间轴
        for subtitle in subtitles:
            subtitle_index += 1
            adjusted_start = subtitle['start_seconds'] + current_time_offset
            adjusted_end = subtitle['end_seconds'] + current_time_offset
            
            combined_subtitles.append({
                'index': subtitle_index,
                'start_time': format_srt_time(adjusted_start),
                'end_time': format_srt_time(adjusted_end),
                'text': subtitle['text']
            })
        
        # 获取音频时长，用于计算下一个章节的时间偏移
        if audio_file.exists():
            audio_duration = get_audio_duration(str(audio_file))
            print(f"  - 音频时长: {audio_duration:.2f} 秒")
            
            # 如果音频时长大于字幕的最后时间，使用音频时长
            if subtitles:
                last_subtitle_time = subtitles[-1]['end_seconds']
                chapter_duration = max(audio_duration, last_subtitle_time)
            else:
                chapter_duration = audio_duration
        else:
            print(f"警告：章节 {chapter_name} 中未找到 ori_audio.wav 文件")
            # 如果没有音频文件，使用字幕的最后时间
            if subtitles:
                chapter_duration = subtitles[-1]['end_seconds']
            else:
                chapter_duration = 0
        
        current_time_offset += chapter_duration
        print(f"  - 章节总时长: {chapter_duration:.2f} 秒")
        print(f"  - 累计时长: {current_time_offset:.2f} 秒")
    
    # 写入合并的字幕文件
    print(f"\n开始写入合并字幕文件: {output_file}")
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, subtitle in enumerate(combined_subtitles):
            f.write(f"{subtitle['index']}\n")
            f.write(f"{subtitle['start_time']} --> {subtitle['end_time']}\n")
            f.write(f"{subtitle['text']}\n")
            if i < len(combined_subtitles) - 1:
                f.write("\n")
    
    print(f"成功合并 {len(combined_subtitles)} 条字幕")
    print(f"总时长: {current_time_offset:.2f} 秒 ({current_time_offset/60:.2f} 分钟)")
    print(f"输出文件: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='拼接归档文件夹中的字幕文件')
    parser.add_argument('--archive-dir', '-d', default='归档', 
                      help='归档文件夹路径 (默认: 归档)')
    parser.add_argument('--output', '-o', default='combined.srt',
                      help='输出文件名 (默认: combined.srt)')
    
    args = parser.parse_args()
    
    concatenate_subtitles(args.archive_dir, args.output)

if __name__ == '__main__':
    main()
