# 视频字幕合成工具使用说明

## 功能特点

- 支持将SRT字幕文件合成到MP4视频中
- 支持多种视频格式：MP4, AVI, MKV, MOV, WMV, FLV
- 支持多种字幕格式：SRT, ASS, SSA, VTT
- 可自定义字幕样式（字体大小、颜色、位置等）
- 支持单文件处理和批量处理
- 交互式命令行界面

## 环境要求

### 1. 安装FFmpeg

**Windows:**
1. 从 https://ffmpeg.org/download.html 下载FFmpeg
2. 解压到 `C:\ffmpeg\`
3. 将 `C:\ffmpeg\bin` 添加到系统PATH环境变量

**macOS:**
```bash
brew install ffmpeg
```

**Ubuntu/Debian:**
```bash
sudo apt install ffmpeg
```

### 2. 安装Python依赖
```bash
pip install -r requirements_video_merger.txt
```

## 使用方法

### 1. 命令行模式

#### 单文件合成
```bash
python video_subtitle_merger.py --video input.mp4 --subtitle input.srt --output output.mp4
```

#### 批量处理
```bash
python video_subtitle_merger.py --batch /path/to/video/directory
```

#### 自定义字幕样式
```bash
python video_subtitle_merger.py \
  --video input.mp4 \
  --subtitle input.srt \
  --output output.mp4 \
  --font-size 24 \
  --font-color yellow \
  --outline-color black \
  --outline-width 2 \
  --position bottom
```

### 2. 交互式模式
直接运行脚本，按提示操作：
```bash
python video_subtitle_merger.py
```

### 3. Python代码调用
```python
from video_subtitle_merger import VideoSubtitleMerger

merger = VideoSubtitleMerger()

# 单文件合成
success = merger.merge_subtitle(
    video_path='input.mp4',
    subtitle_path='input.srt',
    output_path='output.mp4',
    font_size=20,
    font_color='white',
    position='bottom'
)

# 批量处理
count = merger.batch_merge(input_dir='./videos/')
```

## 参数说明

| 参数 | 说明 | 默认值 | 可选值 |
|------|------|--------|--------|
| --video, -v | 输入视频文件路径 | - | 任意视频文件 |
| --subtitle, -s | 字幕文件路径 | - | SRT/ASS/SSA/VTT文件 |
| --output, -o | 输出视频路径 | 自动生成 | 任意路径 |
| --batch, -b | 批量处理目录 | - | 目录路径 |
| --font-size | 字体大小 | 20 | 数字 |
| --font-color | 字体颜色 | white | white/black/red/green/blue/yellow/cyan/magenta |
| --outline-color | 轮廓颜色 | black | 同字体颜色 |
| --outline-width | 轮廓宽度 | 1 | 数字 |
| --position | 字幕位置 | bottom | bottom/top/center |

## 使用示例

### 示例1：处理单个文件
```bash
# 基本用法
python video_subtitle_merger.py -v "归档/chapter_011/ori_video.mp4" -s "split_srt/chapter_011_with_speaker.srt"

# 自定义输出路径和样式
python video_subtitle_merger.py \
  -v "归档/chapter_011/ori_video.mp4" \
  -s "split_srt/chapter_011_with_speaker.srt" \
  -o "output/chapter_011_final.mp4" \
  --font-size 24 \
  --font-color yellow
```

### 示例2：批量处理
```bash
# 批量处理归档目录下的所有章节
python video_subtitle_merger.py --batch "归档/"
```

### 示例3：Python代码集成
```python
import os
from video_subtitle_merger import VideoSubtitleMerger

def process_all_chapters():
    merger = VideoSubtitleMerger()
    
    # 处理所有章节
    for i in range(2, 12):  # chapter_002 到 chapter_011
        chapter_name = f"chapter_{i:03d}"
        video_path = f"归档/{chapter_name}/ori_video.mp4"
        subtitle_path = f"split_srt/{chapter_name}_with_speaker.srt"
        output_path = f"output/{chapter_name}_final.mp4"
        
        if os.path.exists(video_path) and os.path.exists(subtitle_path):
            print(f"Processing {chapter_name}...")
            success = merger.merge_subtitle(
                video_path=video_path,
                subtitle_path=subtitle_path,
                output_path=output_path,
                font_size=22,
                font_color='white',
                outline_color='black',
                outline_width=2
            )
            
            if success:
                print(f"✅ {chapter_name} processed successfully")
            else:
                print(f"❌ {chapter_name} processing failed")

if __name__ == '__main__':
    process_all_chapters()
```

## 注意事项

1. **FFmpeg依赖**：必须先安装FFmpeg才能使用本工具
2. **文件路径**：建议使用绝对路径，避免中文路径可能导致的问题
3. **输出格式**：输出视频统一为MP4格式，使用H.264编码
4. **字幕编码**：字幕文件应使用UTF-8编码
5. **批量处理**：批量模式会自动匹配同名的视频和字幕文件

## 故障排除

### 1. FFmpeg未找到
```
错误: 未找到FFmpeg，请确保FFmpeg已安装并添加到PATH环境变量中
```
**解决方案**：确保FFmpeg正确安装并添加到系统PATH

### 2. 文件不存在
```
错误: 视频文件不存在: xxx.mp4
```
**解决方案**：检查文件路径是否正确

### 3. 编码错误
```
错误信息: Invalid data found when processing input
```
**解决方案**：检查字幕文件编码，确保使用UTF-8编码

### 4. 权限问题
```
错误: Permission denied
```
**解决方案**：确保对输出目录有写入权限


