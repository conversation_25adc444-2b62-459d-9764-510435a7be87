from google import genai
from google.genai import types

client = genai.Client(api_key="AIzaSyC0AcyESb-eqrCgKw7YYs9rW0jAZF2kQL8")


# Read the matched subtitles file
with open('归档/chapter_002/matched_subtitles.txt', 'r', encoding='utf-8') as f:
    matched_subtitles = f.read()

with open('归档/chapter_002/ori_audio.wav', 'rb') as f:
    audio_bytes = f.read()


prompt = """
# 音频字幕纠错与补全任务

## 任务目标
基于提供的音频内容，对字幕文本进行精确纠错和补全，输出标准化的JSON格式结果。

## 具体要求

### 1. 纠错内容
- **发言人识别**：准确识别并标注每段台词的发言人（人名或角色名）
- **台词内容**：修正转录错误、语法问题和表达不准确之处
- **遗漏补充**：识别并补充音频中存在但字幕中缺失的台词内容

### 2. 输出格式
- 采用JSON格式，结构类似SRT字幕格式
- 包含：序号、时间轴、发言人、台词内容

### 3. 时间轴处理规则
- **严格保持**：不得修改原有字幕的时间轴信息
- **合理插入**：补充台词时，确保新增时间轴与前后台词时间逻辑一致
- **无缝衔接**：保证整体时间轴的连续性和准确性

### 4. 质量标准
- 台词与音频内容完全匹配
- 发言人标注准确无误
- 补充内容自然融入原字幕结构
- 时间轴精确对应音频节点

## 输入数据
原字幕文本：{matched_subtitles}

## 输出要求
请严格按照上述要求输出优化后的JSON格式字幕数据。
""".format(matched_subtitles=matched_subtitles)

print(prompt)
response = client.models.generate_content(
  model='gemini-2.5-pro',
  contents=[
    # f'根据提供的音频，对提供的字幕文本发言人和台词进行纠错,输出结果是json格式，和srt字幕相似。发言人对应人名或角色，如果有漏掉未识别的台词请补充，并保证补充的台词能够正确插入台词字幕中。\n原字幕文本：\n{matched_subtitles}。不要修改字幕文本的时间轴，但是可以在保证时间轴正确的情况下，添加未识别的台词。',
    prompt,
    types.Part.from_bytes(
      data=audio_bytes,
      mime_type='audio/wav',
    ),
  ],
  config=types.GenerateContentConfig(
        temperature=1,
    ),

)

print(response)

print(response.text)