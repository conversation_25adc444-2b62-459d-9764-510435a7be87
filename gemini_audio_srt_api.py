import os
import json
from typing import Optional
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field
from google import genai
from google.genai.types import (
    FunctionDeclaration,
    GenerateContentConfig,
    GoogleSearch,
    HarmBlockThreshold,
    HarmCategory,
    MediaResolution,
    Part,
    Retrieval,
    SafetySetting,
    Tool,
    ToolCodeExecution,
    VertexAISearch,
)

# FastAPI app instance
app = FastAPI(
    title="Gemini Audio SRT API",
    description="API for processing audio files with SRT subtitles using Google Gemini",
    version="1.0.0"
)

# Constants
PROJECT_ID = "gen-lang-client-0255558294"
LOCATION = os.environ.get("GOOGLE_CLOUD_REGION", "global")
MODEL_ID = "gemini-2.5-pro"

# Initialize Gemini client
client = genai.Client(vertexai=True, project=PROJECT_ID, location=LOCATION)

# Default prompt template
DEFAULT_PROMPT_TEMPLATE = """根据这段音频，参考提供的srt字幕，并按照srt字幕的格式,获取音频中的说话人，台词。返回字段包含spearker, start_time, end_time, text。返回json格式。

*要求：
1. 不要修改参考SRT文件内容的时间轴。
2. 可以在时间轴正确且不修改原时间轴的前提下，添加未识别的台词，并添加到合适的时间轴上。
3. 可以修改参考SRT文件识别错的台词，不要添加语气词、表情内容，只需要人物说话的台词。
4. speaker可以是角色名也可以是指代对象。如果是角色名，需要在上下文中出现。如果是指代对象，需要合理。

*参考的SRT文件内容：
{srt_content}

请根据音频内容生成准确的字幕信息。"""

# Request model
class AudioSRTRequest(BaseModel):
    file_url: str = Field(..., description="音频文件的URL地址", min_length=1)
    srt_file: str = Field(..., description="SRT字幕文件内容", min_length=1)
    prompt: Optional[str] = Field(
        default=None, 
        description="自定义提示词，如果不提供则使用默认提示词"
    )

# Response model
class AudioSRTResponse(BaseModel):
    output: str = Field(..., description="处理后的输出结果")
    status: str = Field(default="success", description="处理状态")

@app.post("/process-audio-srt", response_model=AudioSRTResponse)
async def process_audio_srt(request: AudioSRTRequest):
    """
    处理音频文件和SRT字幕，生成带有说话人信息的字幕
    
    Args:
        request: 包含文件URL、SRT内容和可选提示词的请求对象
        
    Returns:
        AudioSRTResponse: 包含处理结果的响应对象
        
    Raises:
        HTTPException: 当处理过程中出现错误时
    """
    try:
        # 使用提供的prompt或默认prompt
        if request.prompt:
            prompt = request.prompt
        else:
            prompt = DEFAULT_PROMPT_TEMPLATE.format(srt_content=request.srt_file)
        
        # 调用Gemini API
        response = client.models.generate_content(
            model=MODEL_ID,
            contents=[
                Part.from_uri(
                    file_uri=request.file_url,
                    mime_type="audio/mpeg",
                ),
                prompt,
            ],
            config=GenerateContentConfig(audio_timestamp=True, temperature=1.0),
        )
        
        # 检查响应是否有效
        if not response.text:
            raise HTTPException(
                status_code=500, 
                detail="Gemini API 返回空响应"
            )
        
        # 尝试解析JSON响应
        try:
            json_data = json.loads(response.text)
            output = json.dumps(json_data, ensure_ascii=False, indent=2)
        except json.JSONDecodeError:
            # 如果无法解析为JSON，返回原始文本
            output = response.text
        
        return AudioSRTResponse(
            output=output,
            status="success"
        )
        
    except Exception as e:
        # 记录错误并返回HTTP异常
        error_message = f"处理过程中出现错误: {str(e)}"
        raise HTTPException(status_code=500, detail=error_message)

@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "service": "Gemini Audio SRT API"}

@app.get("/")
async def root():
    """根端点，返回API信息"""
    return {
        "message": "Gemini Audio SRT API",
        "version": "1.0.0",
        "endpoints": {
            "process": "/process-audio-srt",
            "health": "/health",
            "docs": "/docs"
        }
    }

# 可选：添加CORS支持
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该设置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
