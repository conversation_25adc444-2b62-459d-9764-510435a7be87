import os
import json
from google import genai
from google.genai.types import (
    FunctionDeclaration,
    GenerateContentConfig,
    GoogleSearch,
    HarmBlockThreshold,
    HarmCategory,
    MediaResolution,
    Part,
    Retrieval,
    SafetySetting,
    Tool,
    ToolCodeExecution,
    VertexAISearch,
)

PROJECT_ID = "gen-lang-client-0255558294"

LOCATION = os.environ.get("GOOGLE_CLOUD_REGION", "global")

client = genai.Client(vertexai=True, project=PROJECT_ID, location=LOCATION)

MODEL_ID = "gemini-2.5-pro"

# 读取SRT文件内容
def read_srt_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except FileNotFoundError:
        print(f"文件 {file_path} 未找到")
        return ""
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return ""

# 读取SRT文件
srt_content = read_srt_file("merged_all_chapters.srt")

# 构建包含SRT内容的prompt
prompt = f"""根据这段音频，参考提供的srt字幕，并按照srt字幕的格式,识别音频中的说话人，台词。返回字段包含spearker, start_time, end_time, gender, text。返回json格式。

**要求**：
1. 不要修改参考SRT文件内容的时间轴。时间轴完全和原来的字幕保持完全一致，不要修改序号，不要修改时间轴。不要自作主张拆分台词。保证输出的时间轴和参考的SRT文件时间轴完全一致。
2. 根据上下文识别speaker，要求speaker能做到上下文合理。speaker需要为中文。
3. start_time, end_time格式为00:14:03,557，注意逗号是英文逗号。
4. gender为男或女。根据音频识别说话人的性别。

*参考的SRT文件内容：
{srt_content}

请根据音频内容生成准确的字幕信息。"""


response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(
            file_uri="https://gidfyoqecthbormpbirt.supabase.co/storage/v1/object/public/audios/merged_0902.mp3",
            mime_type="audio/mpeg",
        ),
        prompt,
    ],
    config=GenerateContentConfig(audio_timestamp=True, temperature=1.0),
)

# 保存JSON输出到文件
try:
    # 解析JSON响应
    json_data = json.loads(response.text)
    
    # 保存到JSON文件
    output_file = "subtitle_output_0902.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    
    print(f"JSON输出已保存到 {output_file}")
    print("输出内容:")
    print(json.dumps(json_data, ensure_ascii=False, indent=2))
    
except json.JSONDecodeError as e:
    print(f"JSON解析错误: {e}")
    print("原始响应:")
    print(response.text)
    
    # 如果JSON解析失败，保存原始文本
    with open("subtitle_output_raw_0902.txt", 'w', encoding='utf-8') as f:
        f.write(response.text)
    print("原始响应已保存到 subtitle_output_raw_0902.txt")
    
except Exception as e:
    print(f"保存文件时出错: {e}")
    print("原始响应:")
    print(response.text)