import os
import json
import re
from datetime import datetime
from google import genai
from google.genai.types import (
    FunctionDeclaration,
    GenerateContentConfig,
    GoogleSearch,
    HarmBlockThreshold,
    HarmCategory,
    MediaResolution,
    Part,
    Retrieval,
    SafetySetting,
    Tool,
    ToolCodeExecution,
    VertexAISearch,
)

PROJECT_ID = "gen-lang-client-0255558294"

LOCATION = os.environ.get("GOOGLE_CLOUD_REGION", "global")

client = genai.Client(vertexai=True, project=PROJECT_ID, location=LOCATION)

MODEL_ID = "gemini-2.5-pro"



with open('归档/chapter_002/ori_audio.wav', 'rb') as f:
    audio_bytes = f.read()


prompt = """
请你听完全部音频，生成srt字幕。要求返回json格式，字段包含speaker, start_time, end_time, text。

*要求：
1. speaker可以是角色名也可以是指代对象。如果是角色名，需要在上下文中出现。如果是指代对象，需要合理。speaker为中文。
2. start_time, end_time, text 字段需要准确。
3. 识别的内容不要漏掉任何台词。随着音频时间轴，识别台词。不要漏任何时间轴上的信息。
"""

response = client.models.generate_content(
    model=MODEL_ID,
    contents=[
        Part.from_uri(
            file_uri="https://gidfyoqecthbormpbirt.supabase.co/storage/v1/object/public/audios/audio_002.wav",
            mime_type="audio/mpeg",
        ),
        prompt,
    ],
    config=GenerateContentConfig(audio_timestamp=True, temperature=1.0),
)

# 打印原始响应
print("原始响应:")
print(response.text)
print("\n" + "="*50 + "\n")

# 保存JSON文件
def save_response_to_json(response_text, chapter_name="chapter_002"):
    """
    保存Gemini响应到JSON文件
    
    Args:
        response_text: Gemini API的响应文本
        chapter_name: 章节名称，用于文件命名
    """
    try:
        # 尝试提取JSON内容
        json_content = None
        
        # 如果响应包含markdown代码块，提取其中的JSON
        if "```json" in response_text:
            json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                json_content = json_match.group(1).strip()
        elif "```" in response_text:
            # 如果只有代码块但没有标记json
            json_match = re.search(r'```\s*(.*?)\s*```', response_text, re.DOTALL)
            if json_match:
                json_content = json_match.group(1).strip()
        else:
            # 如果没有代码块，尝试直接解析
            json_content = response_text.strip()
        
        if json_content:
            # 验证JSON格式
            parsed_data = json.loads(json_content)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"subtitle_output_{chapter_name}_{timestamp}.json"
            
            # 保存格式化的JSON
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(parsed_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ JSON文件已保存: {output_file}")
            print(f"📊 包含 {len(parsed_data)} 条字幕记录")
            
            # 显示前几条记录作为预览
            if len(parsed_data) > 0:
                print("\n📝 前3条记录预览:")
                for i, item in enumerate(parsed_data[:3]):
                    print(f"  {i+1}. {item.get('speaker', 'Unknown')}: {item.get('text', '')[:50]}...")
            
            return output_file, parsed_data
        else:
            print("❌ 无法从响应中提取JSON内容")
            return None, None
            
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        # 保存原始响应以便调试
        error_file = f"response_error_{chapter_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(error_file, 'w', encoding='utf-8') as f:
            f.write(response_text)
        print(f"原始响应已保存到: {error_file}")
        return None, None
    except Exception as e:
        print(f"❌ 保存文件时出错: {e}")
        return None, None

# 保存响应到JSON文件
output_file, parsed_data = save_response_to_json(response.text, "chapter_002")

if parsed_data:
    # 还可以保存为原始格式（带markdown标记）
    raw_output_file = f"subtitle_output_raw_chapter_002_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(raw_output_file, 'w', encoding='utf-8') as f:
        f.write(response.text)
    print(f"📄 原始响应已保存: {raw_output_file}")