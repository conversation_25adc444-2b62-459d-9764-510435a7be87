# 自动分割功能说明

## 功能概述

在 `merge_audio_with_gemini_role.py` 中新增了自动分割功能，当时间轴匹配结果为100%时，会自动执行 `split_subtitles_by_episodes.py` 的分割功能并保存结果。

## 工作流程

1. **音频字幕合并** - 合并归档目录中的音频和字幕文件
2. **Gemini角色分析** - 使用Gemini API分析音频中的说话人
3. **时间轴比对** - 比较原始字幕和Gemini分析结果的时间轴匹配度
4. **自动判断**：
   - ✅ **匹配率 = 100%** → 自动执行分割功能
   - ⚠️ **匹配率 < 100%** → 提示重试，不执行分割

## 使用方法

### 基本使用
```bash
python merge_audio_with_gemini_role.py
```

### 运行条件
确保以下文件存在：
- `归档/` 目录及其中的章节文件夹
- `merge_timeline.json` (时间轴信息文件)
- 各章节的 `ori_audio.wav` 和 `ori.srt` 文件

## 输出结果

### 当匹配率为100%时
程序会自动执行以下操作：

1. **显示匹配结果**
   ```
   📊 时间轴匹配率: 100.0%
   🎯 时间轴匹配率为100%，开始执行字幕分割...
   ```

2. **自动分割字幕**
   - 加载时间轴信息
   - 加载Gemini分析的字幕数据
   - 按分集分割字幕
   - 创建总结报告

3. **生成文件**
   在 `episodes_subtitles/` 目录中生成：
   - `chapter_XXX.srt` - 标准SRT格式
   - `chapter_XXX_with_speaker.srt` - 带说话人的SRT格式
   - `chapter_XXX_with_speaker.json` - JSON格式（含说话人信息）
   - `split_summary.json` - 分割总结报告

### 当匹配率不足100%时
程序会显示建议：
```
⚠️ 时间轴匹配率为 XX.X%，未达到100%
💡 建议:
   1. 检查音频和字幕是否完全对应
   2. 重新运行Gemini分析
   3. 手动检查差异项目
   4. 如需强制执行分割，请手动运行 split_subtitles_by_episodes.py
```

## 手动分割选项

如果需要强制执行分割（即使匹配率不是100%），可以手动运行：
```bash
python split_subtitles_by_episodes.py
```

## 配置参数

在 `merge_audio_with_gemini_role.py` 的 `main()` 函数中可以修改：

```python
# 默认配置
timeline_file = "merge_timeline.json"      # 时间轴文件
output_dir = "episodes_subtitles"          # 输出目录
```

## 文件结构示例

```
项目目录/
├── merge_audio_with_gemini_role.py        # 主程序
├── split_subtitles_by_episodes.py         # 分割功能
├── merge_timeline.json                    # 时间轴信息
├── 归档/                                  # 音频字幕源文件
│   ├── chapter_002/
│   │   ├── ori_audio.wav
│   │   └── ori.srt
│   └── chapter_003/
│       ├── ori_audio.wav
│       └── ori.srt
└── episodes_subtitles/                    # 自动生成的分割结果
    ├── chapter_002.srt
    ├── chapter_002_with_speaker.srt
    ├── chapter_002_with_speaker.json
    └── split_summary.json
```

## 注意事项

1. **时间轴文件必需** - 如果 `merge_timeline.json` 不存在，即使匹配率100%也不会执行分割
2. **匹配率计算** - 基于时间轴的完全匹配，不考虑文本内容差异
3. **自动覆盖** - 分割结果会覆盖 `episodes_subtitles/` 目录中的同名文件
4. **错误处理** - 分割过程中的任何错误都会被捕获并显示

## 测试功能

可以使用测试脚本验证功能：
```bash
python test_simple_matching.py
```

这将显示当前的匹配率和是否满足自动分割条件。
