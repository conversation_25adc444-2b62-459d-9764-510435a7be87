#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
角色语音提取工具
根据字幕时间轴和角色名从音频中提取不同角色的语音并归类保存
"""

import os
import json
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Optional, Union
from datetime import datetime, timedelta
import tempfile
import shutil

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CharacterVoiceExtractor:
    """角色语音提取器"""
    
    def __init__(self, output_format: str = 'wav'):
        """
        初始化角色语音提取器
        
        Args:
            output_format: 输出音频格式 ('wav', 'mp3', 'flac')
        """
        self.output_format = output_format.lower()
        self.supported_formats = ['wav', 'mp3', 'flac', 'aac']
        
        if self.output_format not in self.supported_formats:
            raise ValueError(f"不支持的音频格式: {output_format}")
        
        # 检查ffmpeg是否可用
        self._check_ffmpeg()
        
    def _check_ffmpeg(self):
        """检查ffmpeg是否安装"""
        try:
            subprocess.run(['ffmpeg', '-version'], 
                         capture_output=True, check=True)
            logger.info("FFmpeg检测成功")
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("FFmpeg未找到，请先安装FFmpeg")
            logger.info("Windows安装方法: https://ffmpeg.org/download.html")
            logger.info("或使用chocolatey: choco install ffmpeg")
            raise RuntimeError("FFmpeg未安装")
    
    def _parse_time_to_seconds(self, time_str: str) -> float:
        """
        将时间字符串转换为秒数
        
        Args:
            time_str: 时间字符串，格式如 "00:01:23.456"
            
        Returns:
            秒数
        """
        try:
            # 分割时:分:秒.毫秒
            parts = time_str.split(':')
            if len(parts) != 3:
                raise ValueError(f"时间格式错误: {time_str}")
            
            hours = int(parts[0])
            minutes = int(parts[1])
            
            # 处理秒和毫秒
            sec_parts = parts[2].split('.')
            seconds = int(sec_parts[0])
            milliseconds = int(sec_parts[1]) if len(sec_parts) > 1 else 0
            
            total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
            return total_seconds
            
        except Exception as e:
            logger.error(f"时间解析错误 '{time_str}': {e}")
            raise
    
    def load_subtitle_data(self, subtitle_file: str) -> List[Dict]:
        """
        加载字幕数据
        
        Args:
            subtitle_file: 字幕文件路径
            
        Returns:
            字幕数据列表
        """
        try:
            # 读取文件内容
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            # 如果内容以```json开头，去掉markdown格式
            if content.startswith('```json'):
                content = content[7:]  # 去掉```json
            if content.endswith('```'):
                content = content[:-3]  # 去掉结尾的```
            
            # 解析JSON
            subtitle_data = json.loads(content)
            
            logger.info(f"成功加载 {len(subtitle_data)} 条字幕记录")
            return subtitle_data
            
        except Exception as e:
            logger.error(f"加载字幕文件失败: {e}")
            raise
    
    def get_characters(self, subtitle_data: List[Dict]) -> List[str]:
        """
        获取所有角色名列表
        
        Args:
            subtitle_data: 字幕数据
            
        Returns:
            角色名列表
        """
        characters = set()
        for item in subtitle_data:
            if 'speaker' in item:
                characters.add(item['speaker'])
        
        return sorted(list(characters))
    
    def extract_character_segments(self, audio_file: str, subtitle_data: List[Dict], 
                                 output_dir: str, target_character: Optional[str] = None,
                                 merge_close_segments: bool = False, 
                                 min_gap_seconds: float = 0.5) -> Dict[str, List[str]]:
        """
        提取角色语音片段
        
        Args:
            audio_file: 原始音频文件路径
            subtitle_data: 字幕数据
            output_dir: 输出目录
            target_character: 目标角色（None表示提取所有角色）
            merge_close_segments: 是否合并临近的片段
            min_gap_seconds: 合并片段的最小间隔（秒）
            
        Returns:
            {角色名: [输出文件路径列表]} 的字典
        """
        try:
            # 创建输出目录
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 严格按照每个字典条目进行处理，不进行合并
            character_segments = {}
            segment_index = {}  # 用于记录每个角色的片段序号
            
            for item in subtitle_data:
                speaker = item.get('speaker', '未知')
                
                # 如果指定了目标角色，只处理该角色
                if target_character and speaker != target_character:
                    continue
                
                if speaker not in character_segments:
                    character_segments[speaker] = []
                    segment_index[speaker] = 0
                
                try:
                    start_time = self._parse_time_to_seconds(item['start_time'])
                    end_time = self._parse_time_to_seconds(item['end_time'])
                    
                    # 为每个片段添加唯一标识
                    segment_index[speaker] += 1
                    character_segments[speaker].append({
                        'start': start_time,
                        'end': end_time,
                        'text': item['text'],
                        'index': segment_index[speaker],
                        'original_start_time': item['start_time'],
                        'original_end_time': item['end_time']
                    })
                except Exception as e:
                    logger.warning(f"跳过无效时间片段: {e}")
                    continue
            
            # 提取音频片段
            results = {}
            
            for speaker, segments in character_segments.items():
                logger.info(f"处理角色: {speaker} ({len(segments)} 个片段)")
                
                # 创建角色专用目录
                character_dir = output_path / speaker
                character_dir.mkdir(exist_ok=True)
                
                character_files = []
                
                # 严格按照每个字典条目提取，不进行合并
                for segment in segments:
                    # 生成输出文件名 - 使用原始时间和序号
                    start_time_str = segment['original_start_time'].replace(':', '-').replace('.', '-')
                    output_file = character_dir / f"{speaker}_{segment['index']:03d}_{start_time_str}.{self.output_format}"
                    
                    # 提取音频片段
                    success = self._extract_audio_segment(
                        audio_file, str(output_file), 
                        segment['start'], segment['end']
                    )
                    
                    if success:
                        character_files.append(str(output_file))
                        logger.info(f"提取成功: {output_file.name} [{segment['original_start_time']} - {segment['original_end_time']}]")
                        logger.info(f"  内容: {segment['text']}")
                    else:
                        logger.warning(f"提取失败: {output_file.name}")
                
                results[speaker] = character_files
                
                # 可选：创建合并文件（只有在用户明确要求时才创建）
                if merge_close_segments and len(character_files) > 1:
                    merged_file = character_dir / f"{speaker}_merged.{self.output_format}"
                    if self._merge_audio_files(character_files, str(merged_file)):
                        results[speaker].append(str(merged_file))
                        logger.info(f"创建合并文件: {merged_file.name}")
            
            return results
            
        except Exception as e:
            logger.error(f"提取角色语音失败: {e}")
            raise
    
    def _merge_close_segments(self, segments: List[Dict], min_gap_seconds: float) -> List[Dict]:
        """
        合并时间间隔较近的片段
        
        Args:
            segments: 片段列表
            min_gap_seconds: 最小间隔秒数
            
        Returns:
            合并后的片段列表
        """
        if not segments:
            return segments
        
        merged = [segments[0].copy()]
        
        for current in segments[1:]:
            last_merged = merged[-1]
            
            # 检查是否应该合并
            gap = current['start'] - last_merged['end']
            
            if gap <= min_gap_seconds:
                # 合并片段
                last_merged['end'] = current['end']
                last_merged['text'] += ' ' + current['text']
            else:
                # 添加新片段
                merged.append(current.copy())
        
        return merged
    
    def _extract_audio_segment(self, input_file: str, output_file: str, 
                             start_time: float, end_time: float) -> bool:
        """
        提取音频片段
        
        Args:
            input_file: 输入音频文件
            output_file: 输出音频文件
            start_time: 开始时间（秒）
            end_time: 结束时间（秒）
            
        Returns:
            是否成功
        """
        try:
            duration = end_time - start_time
            
            if duration <= 0:
                logger.warning(f"无效的时间范围: {start_time} - {end_time}")
                return False
            
            # 构建ffmpeg命令
            cmd = [
                'ffmpeg',
                '-i', input_file,
                '-ss', str(start_time),
                '-t', str(duration),
                '-c', 'copy',  # 尽量避免重新编码
                '-avoid_negative_ts', 'make_zero',
                '-y',  # 覆盖输出文件
                output_file
            ]
            
            # 如果需要格式转换，使用重新编码
            input_ext = Path(input_file).suffix.lower()
            output_ext = Path(output_file).suffix.lower()
            
            if input_ext != output_ext:
                # 移除copy选项，使用重新编码
                cmd = [c for c in cmd if c != '-c' and c != 'copy']
                
                # 添加音频编码参数
                if self.output_format == 'mp3':
                    cmd.extend(['-acodec', 'libmp3lame', '-b:a', '192k'])
                elif self.output_format == 'wav':
                    cmd.extend(['-acodec', 'pcm_s16le'])
                elif self.output_format == 'flac':
                    cmd.extend(['-acodec', 'flac'])
            
            # 执行命令（修复Windows编码问题）
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  encoding='utf-8', errors='ignore')
            
            if result.returncode != 0:
                logger.error(f"FFmpeg错误: {result.stderr}")
                return False
            
            # 检查输出文件是否存在且有内容
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                return True
            else:
                logger.error(f"输出文件创建失败或为空: {output_file}")
                return False
                
        except Exception as e:
            logger.error(f"提取音频片段失败: {e}")
            return False
    
    def _merge_audio_files(self, input_files: List[str], output_file: str) -> bool:
        """
        合并多个音频文件
        
        Args:
            input_files: 输入文件列表
            output_file: 输出文件
            
        Returns:
            是否成功
        """
        try:
            if len(input_files) < 2:
                return False
            
            # 创建临时文件列表
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                for file_path in input_files:
                    # 在Windows上，FFmpeg需要使用正斜杠路径
                    abs_path = os.path.abspath(file_path).replace('\\', '/')
                    f.write(f"file '{abs_path}'\n")
                list_file = f.name
            
            try:
                # 使用ffmpeg concat协议合并
                cmd = [
                    'ffmpeg',
                    '-f', 'concat',
                    '-safe', '0',
                    '-i', list_file,
                    '-c', 'copy',
                    '-y',
                    output_file
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True,
                                      encoding='utf-8', errors='ignore')
                
                if result.returncode != 0:
                    logger.error(f"合并音频失败: {result.stderr}")
                    return False
                
                return os.path.exists(output_file) and os.path.getsize(output_file) > 0
                
            finally:
                # 清理临时文件
                if os.path.exists(list_file):
                    os.unlink(list_file)
                
        except Exception as e:
            logger.error(f"合并音频文件失败: {e}")
            return False
    
    def create_character_summary(self, results: Dict[str, List[str]], output_dir: str):
        """
        创建角色语音提取摘要
        
        Args:
            results: 提取结果
            output_dir: 输出目录
        """
        try:
            summary = {
                'extraction_time': datetime.now().isoformat(),
                'total_characters': len(results),
                'characters': {}
            }
            
            for character, files in results.items():
                # 统计信息
                file_count = len(files)
                total_size = sum(os.path.getsize(f) for f in files if os.path.exists(f))
                
                # 计算总时长（通过文件大小粗略估算）
                estimated_duration = 0
                for file_path in files:
                    if os.path.exists(file_path):
                        try:
                            # 使用ffprobe获取精确时长
                            cmd = [
                                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                                '-show_format', file_path
                            ]
                            result = subprocess.run(cmd, capture_output=True, text=True, 
                                                   check=True, encoding='utf-8', errors='ignore')
                            info = json.loads(result.stdout)
                            duration = float(info['format'].get('duration', 0))
                            estimated_duration += duration
                        except:
                            pass
                
                summary['characters'][character] = {
                    'file_count': file_count,
                    'total_size_mb': round(total_size / 1024 / 1024, 2),
                    'estimated_duration_seconds': round(estimated_duration, 2),
                    'files': [os.path.basename(f) for f in files]
                }
            
            # 保存摘要文件
            summary_file = Path(output_dir) / 'extraction_summary.json'
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            logger.info(f"创建摘要文件: {summary_file}")
            
            # 打印摘要
            print(f"\n{'='*50}")
            print("角色语音提取摘要")
            print(f"{'='*50}")
            print(f"提取时间: {summary['extraction_time']}")
            print(f"总角色数: {summary['total_characters']}")
            print()
            
            for character, info in summary['characters'].items():
                print(f"角色: {character}")
                print(f"  文件数量: {info['file_count']}")
                print(f"  总大小: {info['total_size_mb']} MB")
                print(f"  估算时长: {info['estimated_duration_seconds']:.1f} 秒")
                print()
            
        except Exception as e:
            logger.error(f"创建摘要失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='角色语音提取工具')
    parser.add_argument('audio_file', help='原始音频文件路径')
    parser.add_argument('subtitle_file', help='字幕文件路径（JSON格式）')
    parser.add_argument('-o', '--output', default='character_voices', 
                       help='输出目录，默认为character_voices')
    parser.add_argument('-c', '--character', help='指定要提取的角色名（不指定则提取所有角色）')
    parser.add_argument('-f', '--format', choices=['wav', 'mp3', 'flac'], 
                       default='wav', help='输出音频格式')
    parser.add_argument('--merge', action='store_true', 
                       help='合并临近的片段（默认严格按字典条目提取）')
    parser.add_argument('--min-gap', type=float, default=0.5, 
                       help='合并片段的最小间隔（秒），默认0.5秒')
    
    args = parser.parse_args()
    
    try:
        # 创建提取器
        extractor = CharacterVoiceExtractor(args.format)
        
        # 加载字幕数据
        subtitle_data = extractor.load_subtitle_data(args.subtitle_file)
        
        # 显示可用角色
        characters = extractor.get_characters(subtitle_data)
        print(f"发现角色: {', '.join(characters)}")
        
        # 提取语音
        results = extractor.extract_character_segments(
            audio_file=args.audio_file,
            subtitle_data=subtitle_data,
            output_dir=args.output,
            target_character=args.character,
            merge_close_segments=args.merge,
            min_gap_seconds=args.min_gap
        )
        
        # 创建摘要
        extractor.create_character_summary(results, args.output)
        
        print(f"\n提取完成！输出目录: {args.output}")
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
