#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
字幕分割脚本
功能：将合并的speaker_output.txt按照每集的音频长度分割成单独的SRT文件
"""

import os
import json
import wave
from pathlib import Path
from typing import List, Dict, Tuple
import argparse
import re

def parse_srt_time(time_str: str) -> float:
    """
    将SRT时间格式转换为秒数
    格式: HH:MM:SS,mmm
    """
    # 替换逗号为点号
    time_str = time_str.replace(',', '.')
    
    # 分割时间组件
    parts = time_str.split(':')
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = float(parts[2])
    
    total_seconds = hours * 3600 + minutes * 60 + seconds
    return total_seconds

def format_srt_time(seconds: float) -> str:
    """
    将秒数转换为SRT时间格式
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = seconds % 60
    
    # 格式化为 HH:MM:SS,mmm
    return f"{hours:02d}:{minutes:02d}:{secs:06.3f}".replace('.', ',')

def get_audio_duration(audio_path: str) -> float:
    """
    获取音频文件的时长（秒）
    """
    try:
        with wave.open(audio_path, 'rb') as audio_file:
            frames = audio_file.getnframes()
            sample_rate = audio_file.getframerate()
            duration = frames / float(sample_rate)
            return duration
    except Exception as e:
        print(f"警告：无法读取音频文件 {audio_path} 的时长: {e}")
        return 0.0

def load_speaker_output(file_path: str) -> List[Dict]:
    """
    加载speaker_output.txt文件
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            # 移除markdown代码块标记
            if content.startswith('```json'):
                content = content[7:]
            if content.endswith('```'):
                content = content[:-3]
            
            data = json.loads(content)
            return data
    except Exception as e:
        print(f"错误：无法读取文件 {file_path}: {e}")
        return []

def get_chapter_durations(archive_dir: str) -> Dict[str, float]:
    """
    获取各章节的音频时长
    """
    archive_path = Path(archive_dir)
    if not archive_path.exists():
        print(f"错误：归档目录 {archive_dir} 不存在")
        return {}
    
    chapter_durations = {}
    cumulative_duration = 0.0
    
    # 获取所有章节目录
    chapter_dirs = []
    for item in archive_path.iterdir():
        if item.is_dir() and item.name.startswith('chapter_'):
            chapter_dirs.append(item)
    
    # 按章节编号排序
    chapter_dirs.sort(key=lambda x: int(x.name.split('_')[1]))
    
    for chapter_dir in chapter_dirs:
        chapter_name = chapter_dir.name
        audio_file = chapter_dir / 'ori_audio.wav'
        
        if audio_file.exists():
            duration = get_audio_duration(str(audio_file))
            chapter_durations[chapter_name] = {
                'duration': duration,
                'start_time': cumulative_duration,
                'end_time': cumulative_duration + duration
            }
            cumulative_duration += duration
            print(f"章节 {chapter_name}: {duration:.2f}秒 (累计: {cumulative_duration:.2f}秒)")
        else:
            print(f"警告：章节 {chapter_name} 中未找到 ori_audio.wav 文件")
    
    return chapter_durations

def split_subtitles_by_chapters(speaker_data: List[Dict], chapter_durations: Dict[str, float], output_dir: str = "split_srt"):
    """
    根据章节时长分割字幕
    """
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 按章节分组字幕
    chapter_subtitles = {}
    
    # 按章节顺序排序
    sorted_chapters = sorted(chapter_durations.items(), key=lambda x: int(x[0].split('_')[1]))
    
    print(f"章节时间分布:")
    for chapter_name, chapter_info in sorted_chapters:
        print(f"  {chapter_name}: {chapter_info['start_time']:.2f}s - {chapter_info['end_time']:.2f}s")
    print()
    
    for subtitle in speaker_data:
        start_seconds = parse_srt_time(subtitle['start'])
        
        # 找到这条字幕属于哪个章节
        found_chapter = None
        for chapter_name, chapter_info in sorted_chapters:
            if chapter_info['start_time'] <= start_seconds < chapter_info['end_time']:
                found_chapter = chapter_name
                break
        
        # 如果没找到匹配的章节，可能是最后一个章节的字幕
        if not found_chapter:
            # 检查是否属于最后一个章节（允许稍微超出）
            last_chapter_name, last_chapter_info = sorted_chapters[-1]
            if start_seconds >= last_chapter_info['start_time']:
                found_chapter = last_chapter_name
        
        if found_chapter:
            chapter_info = chapter_durations[found_chapter]
            if found_chapter not in chapter_subtitles:
                chapter_subtitles[found_chapter] = []
            
            # 调整时间轴（减去章节开始时间）
            adjusted_start = start_seconds - chapter_info['start_time']
            adjusted_end = parse_srt_time(subtitle['end']) - chapter_info['start_time']
            
            # 确保时间不为负数
            adjusted_start = max(0, adjusted_start)
            adjusted_end = max(0, adjusted_end)
            
            chapter_subtitles[found_chapter].append({
                'start': format_srt_time(adjusted_start),
                'end': format_srt_time(adjusted_end),
                'text': subtitle['text'],
                'speaker': subtitle['speaker']
            })
        else:
            print(f"警告：无法为时间 {subtitle['start']} 找到对应章节")
    
    # 生成每个章节的SRT文件
    for chapter_name, subtitles in chapter_subtitles.items():
        if not subtitles:
            continue
        
        # 生成标准SRT文件
        srt_file = output_path / f"{chapter_name}_with_speaker.srt"
        with open(srt_file, 'w', encoding='utf-8') as f:
            for i, subtitle in enumerate(subtitles, 1):
                f.write(f"{i}\n")
                f.write(f"{subtitle['start']} --> {subtitle['end']}\n")
                f.write(f"[{subtitle['speaker']}] {subtitle['text']}\n")
                if i < len(subtitles):
                    f.write("\n")
        
        # 生成不带说话人的SRT文件
        srt_file_no_speaker = output_path / f"{chapter_name}.srt"
        with open(srt_file_no_speaker, 'w', encoding='utf-8') as f:
            for i, subtitle in enumerate(subtitles, 1):
                f.write(f"{i}\n")
                f.write(f"{subtitle['start']} --> {subtitle['end']}\n")
                f.write(f"{subtitle['text']}\n")
                if i < len(subtitles):
                    f.write("\n")
        
        # 生成JSON格式（保留说话人信息）
        json_file = output_path / f"{chapter_name}_subtitles.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(subtitles, f, ensure_ascii=False, indent=2)
        
        print(f"生成 {chapter_name}: {len(subtitles)} 条字幕")
        print(f"  - {srt_file}")
        print(f"  - {srt_file_no_speaker}")
        print(f"  - {json_file}")

def split_speaker_output(speaker_file: str = "speaker_output.txt", 
                        archive_dir: str = "归档", 
                        output_dir: str = "split_srt"):
    """
    主函数：分割speaker_output.txt为各章节的SRT文件
    """
    print("=== 字幕分割工具 ===")
    print(f"输入文件: {speaker_file}")
    print(f"归档目录: {archive_dir}")
    print(f"输出目录: {output_dir}")
    print()
    
    # 1. 加载speaker输出数据
    print("1. 加载字幕数据...")
    speaker_data = load_speaker_output(speaker_file)

    if not speaker_data:
        print("错误：无法加载字幕数据")
        return
    print(speaker_data)
    print(f"   加载了 {len(speaker_data)} 条字幕")
    
    # 2. 获取章节时长信息
    print("\n2. 获取章节音频时长...")
    chapter_durations = get_chapter_durations(archive_dir)
    if not chapter_durations:
        print("错误：无法获取章节时长信息")
        return
    
    # 3. 分割字幕
    print("\n3. 按章节分割字幕...")
    split_subtitles_by_chapters(speaker_data, chapter_durations, output_dir)
    
    print(f"\n完成！所有文件已保存到 {output_dir} 目录")

def main():
    parser = argparse.ArgumentParser(description='将合并的speaker_output.txt分割为各章节的SRT文件')
    parser.add_argument('--speaker-file', '-s', default='speaker_output.txt',
                      help='speaker输出文件路径 (默认: speaker_output.txt)')
    parser.add_argument('--archive-dir', '-d', default='归档', 
                      help='归档文件夹路径 (默认: 归档)')
    parser.add_argument('--output-dir', '-o', default='split_srt',
                      help='输出目录 (默认: split_srt)')
    
    args = parser.parse_args()
    
    split_speaker_output(args.speaker_file, args.archive_dir, args.output_dir)

if __name__ == '__main__':
    main()
