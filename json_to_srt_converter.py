#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JSON字幕转SRT格式转换器
将JSON格式的字幕数据转换为标准SRT字幕文件
"""

import json
import re
from datetime import datetime, timedelta
import argparse
import os


def time_str_to_milliseconds(time_str):
    """
    将时间字符串转换为毫秒
    支持格式: HH:MM:SS.mmm 或 MM:SS.mmm
    """
    # 移除可能的空格
    time_str = time_str.strip()
    
    # 处理不同的时间格式
    if len(time_str.split(':')) == 3:
        # HH:MM:SS.mmm 格式
        hours, minutes, seconds = time_str.split(':')
    else:
        # MM:SS.mmm 格式，假设小时为0
        parts = time_str.split(':')
        hours = '0'
        minutes = parts[0]
        seconds = parts[1]
    
    # 分离秒和毫秒
    if '.' in seconds:
        sec, ms = seconds.split('.')
        # 确保毫秒是3位数
        ms = ms.ljust(3, '0')[:3]
    else:
        sec = seconds
        ms = '000'
    
    # 转换为总毫秒数
    total_ms = (int(hours) * 3600 + int(minutes) * 60 + int(sec)) * 1000 + int(ms)
    return total_ms


def milliseconds_to_srt_time(ms):
    """
    将毫秒转换为SRT时间格式 (HH:MM:SS,mmm)
    """
    hours = ms // 3600000
    ms %= 3600000
    minutes = ms // 60000
    ms %= 60000
    seconds = ms // 1000
    milliseconds = ms % 1000
    
    return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"


def json_to_srt(json_file_path, output_file_path=None, include_speaker=True):
    """
    将JSON字幕文件转换为SRT格式
    
    Args:
        json_file_path: 输入的JSON文件路径
        output_file_path: 输出的SRT文件路径，如果为None则自动生成
        include_speaker: 是否包含说话人信息
    """
    
    # 读取JSON文件
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 移除可能的markdown代码块标记
        content = re.sub(r'^```json\s*\n', '', content, flags=re.MULTILINE)
        content = re.sub(r'\n```\s*$', '', content, flags=re.MULTILINE)
        
        # 解析JSON数据
        subtitle_data = json.loads(content)
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file_path}")
        return False
    except json.JSONDecodeError as e:
        print(f"错误: JSON格式无效 - {e}")
        return False
    except Exception as e:
        print(f"错误: 读取文件时出现问题 - {e}")
        return False
    
    # 生成输出文件路径
    if output_file_path is None:
        base_name = os.path.splitext(json_file_path)[0]
        output_file_path = f"{base_name}.srt"
    
    # 转换为SRT格式
    try:
        with open(output_file_path, 'w', encoding='utf-8') as f:
            for i, subtitle in enumerate(subtitle_data, 1):
                # 获取字幕信息
                speaker = subtitle.get('speaker', '')
                start_time = subtitle.get('start_time', '')
                end_time = subtitle.get('end_time', '')
                text = subtitle.get('text', '')
                
                # 转换时间格式
                start_ms = time_str_to_milliseconds(start_time)
                end_ms = time_str_to_milliseconds(end_time)
                
                start_srt = milliseconds_to_srt_time(start_ms)
                end_srt = milliseconds_to_srt_time(end_ms)
                
                # 组合说话人和文本
                if include_speaker and speaker:
                    full_text = f"{speaker}: {text}"
                else:
                    full_text = text
                
                # 写入SRT格式
                f.write(f"{i}\n")
                f.write(f"{start_srt} --> {end_srt}\n")
                f.write(f"{full_text}\n\n")
        
        print(f"转换成功! SRT文件已保存到: {output_file_path}")
        print(f"共转换 {len(subtitle_data)} 条字幕")
        return True
        
    except Exception as e:
        print(f"错误: 写入SRT文件时出现问题 - {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='将JSON格式字幕转换为SRT格式')
    parser.add_argument('input_file', help='输入的JSON文件路径')
    parser.add_argument('-o', '--output', help='输出的SRT文件路径 (可选)')
    parser.add_argument('--no-speaker', action='store_true', help='不包含说话人信息')
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        return
    
    # 执行转换
    success = json_to_srt(args.input_file, args.output, not args.no_speaker)
    
    if success:
        print("转换完成!")
    else:
        print("转换失败!")


if __name__ == "__main__":
    # 如果直接运行脚本，可以处理当前目录下的subtitle_output_raw.txt
    if len(os.sys.argv) == 1:
        # 没有命令行参数时的默认行为
        default_input = "subtitle_output_raw.txt"
        if os.path.exists(default_input):
            print(f"正在转换默认文件: {default_input}")
            json_to_srt(default_input)
        else:
            print(f"默认文件 {default_input} 不存在")
            print("使用方法:")
            print("  python json_to_srt_converter.py <输入文件> [-o 输出文件]")
            print("  或直接运行脚本转换 subtitle_output_raw.txt")
    else:
        main()
